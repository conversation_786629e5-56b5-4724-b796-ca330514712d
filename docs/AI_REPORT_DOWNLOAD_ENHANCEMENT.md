# AI报告下载功能扩展与代码重构

## 功能概述

本次修改扩展了 `SysReportServiceImpl.downloadReport` 方法，使其支持AI报告的下载功能。AI报告的内容存储在 `sys_ai_report` 表的 `content` 字段中（HTML格式），需要转换为PDF格式供用户下载。

**重要更新**：为了避免代码重复，我们将HTML转PDF的相关方法统一整合到 `ReportPreserveService` 中，实现代码复用。

## 主要修改

### 1. SysReportServiceImpl 类修改

#### 新增依赖和导入

- 添加了 `ISysAiReportService` 依赖
- 添加了 `ReportPreserveService` 依赖（用于复用HTML转PDF功能）
- 添加了 `ReportTypeEnum` 枚举导入
- 添加了 `SysAiReport` 实体类导入
- **移除了重复的导入**：删除了Jsoup相关导入，因为现在使用ReportPreserveService

#### 核心方法修改
- **`downloadReport` 方法**：增加了报告类型判断逻辑
  - 根据 `sysReport.getType()` 判断是AI报告还是普通报告
  - AI报告调用 `handleAiReportDownload` 方法
  - 普通报告调用 `handleNormalReportDownload` 方法

#### 新增方法

1. **`handleNormalReportDownload`**
   - 处理普通报告下载逻辑
   - 从原有的 `downloadReport` 方法中提取出来
   - 直接使用报告的 `fileId` 进行文件下载

2. **`handleAiReportDownload`**
   - 处理AI报告下载逻辑
   - 从 `sys_ai_report` 表获取HTML内容
   - 调用 `generatePdfFromHtml` 方法生成PDF
   - 上传PDF文件到文件服务
   - 更新下载记录

3. **`generatePdfFromHtml`**
   - 核心HTML转PDF方法
   - 使用Jsoup解析HTML内容
   - 处理HTML中的图片链接（下载并嵌入）
   - 使用Aspose.Words将HTML转换为PDF
   - 设置A4页面格式和页边距

4. **辅助方法**
   - `disableSSLCertificateValidation`：禁用SSL证书验证
   - `processImageLinks`：处理HTML中的图片链接
   - `downloadImage`：下载网络图片
   - `getImageMimeType`：获取图片MIME类型
   - `setFontsSources`：设置字体源
   - `getLicense`：获取Aspose License

## 技术实现细节

### 报告类型判断
```java
final Byte reportType = sysReport.getType();
if (ReportTypeEnum.AI_REPORT.getId().equals(reportType)) {
    // AI报告处理逻辑
    handleAiReportDownload(sysReport, userId, sysUserDownloadId);
} else {
    // 普通报告处理逻辑
    handleNormalReportDownload(sysReport, userId, sysUserDownloadId);
}
```

### HTML转PDF流程
1. 从 `sys_ai_report` 表获取HTML内容
2. 使用Jsoup解析HTML
3. 处理图片链接（下载并转换为Base64嵌入）
4. 创建临时HTML文件
5. 使用Aspose.Words转换为PDF
6. 上传PDF到文件服务
7. 清理临时文件

### 图片处理
- 自动下载HTML中的网络图片
- 转换为Base64格式嵌入到HTML中
- 保持原有的图片尺寸和样式
- 支持多种图片格式（PNG、JPG、GIF、BMP、WebP）

## 异步处理

所有下载处理都在 `docExecutor` 线程池中异步执行，不会阻塞主线程：

```java
docExecutor.execute(() -> {
    try {
        // 下载处理逻辑
    } catch (final Exception e) {
        log.error("报告下载失败", e);
        // 更新下载记录状态为失败
    }
});
```

## 错误处理

- 完整的异常捕获和日志记录
- 下载失败时更新下载记录状态
- 临时文件的自动清理
- SSL证书验证问题的处理

## 兼容性

- 完全兼容现有的普通报告下载功能
- 不影响现有的API接口
- 保持相同的下载记录跟踪机制
- 维持相同的异步处理模式

## 依赖要求

- Aspose.Words：用于HTML到PDF的转换
- Jsoup：用于HTML解析和处理
- 现有的文件服务：用于PDF文件上传和存储

## 测试

创建了 `SysReportServiceImplTest` 测试类，包含：
- AI报告下载逻辑测试
- 普通报告下载逻辑测试
- 报告类型枚举值验证

## 使用说明

1. 用户在前端点击AI报告的下载按钮
2. 系统自动识别报告类型
3. 对于AI报告，系统将HTML内容转换为PDF
4. 生成下载链接供用户下载
5. 下载记录被正确跟踪和统计

## 注意事项

1. 需要确保Aspose.Words的License文件存在（`license.xml`）
2. HTML中的图片链接需要可访问
3. 转换过程可能需要一定时间，建议前端显示加载状态
4. 生成的PDF文件会占用存储空间，建议定期清理
