<?xml version="1.0" encoding="UTF-8"?>
<!--
scan：当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。
scanPeriod：设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒当scan为true时，此属性生效。默认的时间间隔为1分钟。
debug：当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。
-->
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!--自定义颜色配置 此处converterClass引用的是日志颜色类的路径， 此匹配的是第二种控制台色彩输出方式-->
    <conversionRule conversionWord="customcolor" converterClass="com.quantchi.knowledge.center.config.LogbackColorfulConfig"/>
    <!-- 设置某个类的日志级别为OFF -->
    <logger name="org.elasticsearch.client.RestClient" level="OFF"/>
    <logger name="springfox.documentation.swagger.readers.operation.OperationImplicitParameterReader" level="OFF"/>
    <logger name="org.elasticsearch.deprecation.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder" level="OFF"/>

    <!-- 定义日志的要保存的根目录 -->
    <property name="HOME_LOG" value="logs/app.log"/>
    <property name="LOG_HOME" value="logs"/>
    <property name="appName" value="app"/>
    <!-- ch.qos.logback.core.ConsoleAppender 表示控制台输出 -->

    <!--第二种控制台色彩输出方式-->
    <appender name="CONSLOG" class="ch.qos.logback.core.ConsoleAppender">
        <!--
        日志输出格式：
            %d表示日期时间，
            %thread表示线程名，
            %-5level：级别从左显示5个字符宽度
            %logger{50} 表示logger名字最长50个字符，否则按照句点分割。
            %msg：日志消息，
            %n是换行符
        -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>
                %red(%d{yyyy-MM-dd HH:mm:ss.SSS}) %green([%thread]) %blue([%X{traceId}]) %green(%-5level) %boldMagenta(%logger) - %msg%n
            </pattern>
        </encoder>
    </appender>

    <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件   -->
    <!--该配置表示每天生成一个日志文件，保存365天的日志文件。-->
    <appender name="appLogAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 指定日志文件的名称 -->
        <file>${HOME_LOG}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${appName}-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <MaxHistory>365</MaxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志输出格式： -->
        <encoder>
            <pattern>
                %red(%d{yyyy-MM-dd HH:mm:ss.SSS}) %green([%thread]) %blue([%X{traceId}]) %customcolor(%-5level) %boldMagenta(%logger) - %msg%n
            </pattern>
        </encoder>
    </appender>


    <!-- level用来设置打印级别，大小写无关-->
    <root level="info">
        <!-- 控制台输出日志-->
        <appender-ref ref="CONSLOG"/>
        <!-- 打印错误日志 每天-->
        <appender-ref ref="appLogAppender"/>
    </root>
</configuration>
