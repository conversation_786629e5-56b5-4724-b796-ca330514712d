package com.quantchi.knowledge.center.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quantchi.knowledge.ai.bo.QaPromptPageBO;
import com.quantchi.knowledge.ai.client.ChatBotClient;
import com.quantchi.knowledge.ai.entity.Message;
import com.quantchi.knowledge.ai.entity.chatmodel.ChatQueryRequest;
import com.quantchi.knowledge.ai.entity.chatmodel.DpChatQueryRequest;
import com.quantchi.knowledge.ai.entity.response.ChatResponse;
import com.quantchi.knowledge.ai.entity.response.DpChatResponse;
import com.quantchi.knowledge.ai.vo.PromptTemplateConfigQaVO;
import com.quantchi.knowledge.center.annotation.ApiUsageLimit;
import com.quantchi.knowledge.center.bean.dto.SysChatEditDTO;
import com.quantchi.knowledge.center.bean.entity.*;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.vo.*;
import com.quantchi.knowledge.center.common.ResultConvert;
import com.quantchi.knowledge.center.common.ResultInfo;
import com.quantchi.knowledge.center.component.LocalModelComponent;
import com.quantchi.knowledge.center.dao.mysql.SysUserMapper;
import com.quantchi.knowledge.center.service.*;
import com.quantchi.knowledge.center.service.impl.PatentEsInfoService;
import com.quantchi.knowledge.center.util.HttpServletRequestUtil;
import com.quantchi.knowledge.center.util.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.quantchi.knowledge.center.controller.SysFileController.CHAT_DOC_TYPE;
import static com.quantchi.knowledge.center.util.LocalFileUtil.getFileSize;

/**
 * @date 2023/7/29
 */
@RestController
@RequestMapping("/qa")
@Api(tags = "智能问答接口")
@RequiredArgsConstructor
@Slf4j
public class QaInterfaceController {

    private final ChatBotClient chatBotClient;

    private final PatentEsInfoService patentEsInfoService;

    private final ISysChatPreserveService sysChatPreserveService;

    private final ISysFileService sysFileService;

    private final SysUserMapper sysUserMapper;

    private final ISysUserApiUsageService sysUserApiUsageService;

    private final LocalModelComponent localModelComponent;

    private final IAgentConfigService agentConfigService;

    private final IPromptTemplateConfigService promptTemplateConfigService;

    private final IDifyApiService difyApiService;

    @GetMapping("/expertAgentList")
    @ApiOperation("专家智能体列表")
    public ResultInfo<List<ExpertAgentVO>> expertAgentList() {
        // 从数据库中获取智能体列表
        final List<SysAgentConfig> agentList = agentConfigService.getAgentList();
        return ResultConvert.success(agentList.stream().map(ExpertAgentVO::toVO).collect(Collectors.toList()));
    }

    @PostMapping("/pagePrompt")
    @ApiOperation("prompt分页列表")
    public ResultInfo<PageInfo<String>> promptList(@RequestBody final QaPromptPageBO pageBO) {
        final String expertKey = pageBO.getExpertKey();
        if (CharSequenceUtil.isBlank(expertKey)) {
            final String category = pageBO.getCategory();
            PageHelper.startPage(pageBO.getPageNum(), pageBO.getPageSize());
            final Integer type = pageBO.getType();
            final List<PromptTemplateConfig> list = promptTemplateConfigService.list(Wrappers.<PromptTemplateConfig>lambdaQuery()
                    .eq(PromptTemplateConfig::getType, type)
                    .eq(CharSequenceUtil.isNotBlank(category), PromptTemplateConfig::getCategory, category)
                    .orderByAsc(PromptTemplateConfig::getSort));
            final PageInfo<PromptTemplateConfig> configPageInfo = new PageInfo<>(list);
            final List<PromptTemplateConfigQaVO> collect = list.stream().map(PromptTemplateConfigQaVO::toVO).collect(Collectors.toList());
            final PageInfo<String> resultInfo = new PageInfo<>();
            BeanUtils.copyProperties(configPageInfo, resultInfo);
            resultInfo.setList(collect.stream().map(PromptTemplateConfigQaVO::getTitle).collect(Collectors.toList()));
            return ResultConvert.success(resultInfo);
        }
        final DifyApplicationParametersVO applicationParameters = difyApiService.getApplicationParameters(expertKey);
        final List<String> suggestedQuestions = applicationParameters.getSuggestedQuestions();
        final PageInfo<String> resultInfo = new PageInfo<>();
        resultInfo.setTotal(suggestedQuestions.size());
        resultInfo.setList(applicationParameters.getSuggestedQuestions());
        return ResultConvert.success(resultInfo);
    }

    @ApiOperation("流式返回，连续对话")
    @PostMapping(value = "/stream/chats", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @SaCheckLogin
    public Flux<ChatResponse> chatContOfStreamForPatent(@RequestBody final ChatQueryRequest chatQueryRequest) {
        chatBotClient.setPatentEsInfoService(patentEsInfoService);
        return chatBotClient.chatContOfStreamForPatent(chatQueryRequest.getMsg(), chatQueryRequest.getMsgUid(), chatQueryRequest.getPatentId());
    }

    @GetMapping("/possibleQuestion")
    @ApiOperation("可能追问的问题列表")
    public ResultInfo<List<String>> possibleQuestionList(@RequestParam final String msgUid, @RequestParam(required = false) final Boolean reload) {
        return ResultConvert.success(chatBotClient.getPossibleQuestionList(msgUid, reload));
    }

    @ApiOperation("统一流式问答接口")
    @PostMapping(value = "/stream/chatWithThink", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @SaCheckLogin
    @ApiUsageLimit(apiType = 2, isStream = true)
    public Flux<DpChatResponse> chatWithThink(@RequestBody final DpChatQueryRequest chatQueryRequest) {
        final HttpServletResponse response = HttpServletRequestUtil.getResponse();
        // 设置必要的响应头，确保流式传输工作正常
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("Keep-Alive", "timeout=600");
        response.setHeader("Content-Encoding", "none");
        response.setHeader("X-Accel-Buffering", "no"); // 禁用nginx缓冲
        
        // 设置服务依赖
        chatBotClient.setSysChatPreserveService(sysChatPreserveService);
        chatBotClient.setSysFileService(sysFileService);
        chatBotClient.setLocalModelComponent(localModelComponent);
        
        // 设置智能体配置服务，用于从数据库获取API密钥
        chatBotClient.setAgentConfigService(agentConfigService);
        
        // 直接返回流式数据，不进行缓冲或延迟处理
        return chatBotClient.chatWithThink(chatQueryRequest);
    }
    
    @GetMapping("/usage/remaining")
    @ApiOperation("获取AI对话剩余次数")
    @SaCheckLogin
    public ResultInfo<Integer> getRemainingChatUsage() {
        Long userId = StpUtil.getLoginIdAsLong();
        int remainingCount = sysUserApiUsageService.getRemainingUsageCount(userId, ISysUserApiUsageService.API_TYPE_AI_CHAT);
        return ResultConvert.success(remainingCount);
    }

    @PostMapping("/chat/historyList")
    @ApiOperation(value = "用户对话历史记录列表")
    public ResultInfo<List<SysChatPreserveVO>> chatHistoryList() {
        final List<SysChatPreserve> list = sysChatPreserveService.list(Wrappers.<SysChatPreserve>lambdaQuery()
                .eq(SysChatPreserve::getUserId, StpUtil.getLoginIdAsLong())
                .orderByDesc(SysChatPreserve::getCreateTime));
        return ResultConvert.success(list.stream().map(SysChatPreserveVO::toVO)
                .collect(Collectors.toList()));
    }

    @PostMapping("/chat/history")
    @ApiOperation(value = "对话历史记录详情")
    public ResultInfo<Queue<Message>> chatHistory(@RequestParam final String msgUid) {
        final Queue<Message> messageHistory = chatBotClient.getMessageHistory(msgUid);
        return ResultConvert.success(messageHistory);
    }

    @PostMapping("/chat/historyWithFile")
    @ApiOperation(value = "对话历史记录详情（包含文件）")
    public ResultInfo<Map<String, Object>> chatHistoryWithFile(@RequestParam final String msgUid) {
        // 获取对话历史记录
        final Queue<Message> messageHistory = chatBotClient.getMessageHistory(msgUid);

        // 获取当前用户的文件列表
        final List<SysFile> fileList = sysFileService.list(Wrappers.<SysFile>lambdaQuery()
                .eq(SysFile::getSubjectId, msgUid)
                .eq(SysFile::getFileType, CHAT_DOC_TYPE)
                .orderByDesc(SysFile::getCreateTime));

        // 转换为VO对象
        final List<SysFileVO> fileVOList = new ArrayList<>();
        for (final SysFile file : fileList) {
            final SysFileVO fileVO = new SysFileVO();
            BeanUtils.copyProperties(file, fileVO);
            final Long size = file.getFileSize();
            fileVO.setFileSize(getFileSize(size));
            fileVOList.add(fileVO);
        }
        final Map<String, List<SysFileVO>> collect = fileVOList.stream().collect(Collectors.groupingBy(SysFileVO::getDialogueId));

        // 构建返回结果，包含对话历史和文件列表
        // 遍历对话历史，设置对应的文件列表
        final List<Message> messages = new ArrayList<>(messageHistory);
        messages.forEach(item -> {
            final String content = item.getContent();
            final Long timestamp = item.getTimestamp();
            final String key = DigestUtils.md5DigestAsHex((content + timestamp).getBytes(StandardCharsets.UTF_8));
            if (collect.containsKey(key)) {
                item.setFileList(collect.get(key));
            }
        });

        final Map<String, Object> result = new HashMap<>();
        result.put("messageHistory", messages);
        result.put("userFiles", fileVOList);

        return ResultConvert.success(result);
    }

    @PostMapping("/chat/historyEdit")
    @ApiOperation(value = "修改对话历史记录标题")
    public ResultInfo<Boolean> editChatHistory(@RequestBody final SysChatEditDTO sysChatEditDTO) {
        final String msgUid = sysChatEditDTO.getMsgUid();
        final long loginId = StpUtil.getLoginIdAsLong();
        final SysChatPreserve one = sysChatPreserveService.getOne(Wrappers.<SysChatPreserve>lambdaQuery()
                .eq(SysChatPreserve::getUserId, loginId)
                .likeRight(SysChatPreserve::getMsgUid, msgUid));
        if (one == null) {
            throw new BusinessException("找不到对应的历史记录");
        }
        one.setTitle(sysChatEditDTO.getTitle());
        return ResultConvert.success(sysChatPreserveService.updateById(one));
    }

    @PostMapping("/chat/history/delete")
    @ApiOperation(value = "删除对话历史记录")
    public ResultInfo<Boolean> deleteChatHistory(@RequestParam(required = false) final String msgUid) {
        final long loginId = StpUtil.getLoginIdAsLong();
        if (CharSequenceUtil.isBlank(msgUid)) {
            final List<SysChatPreserve> list = sysChatPreserveService.list(Wrappers.<SysChatPreserve>lambdaQuery()
                    .eq(SysChatPreserve::getUserId, loginId));
            for (final SysChatPreserve sysChatPreserve : list) {
                final Boolean redisRemove = RedisUtils.removeDialogueRecord(loginId, sysChatPreserve.getMsgUid());
                final boolean mysqlRemove = sysChatPreserveService.remove(Wrappers.<SysChatPreserve>lambdaQuery()
                        .eq(SysChatPreserve::getUserId, loginId)
                        .likeRight(SysChatPreserve::getMsgUid, sysChatPreserve.getMsgUid()));
                if (redisRemove && mysqlRemove) {
                    continue;
                }
                return ResultConvert.success(false);
            }
            return ResultConvert.success(true);
        } else {
            final boolean mysqlRemove = sysChatPreserveService.remove(Wrappers.<SysChatPreserve>lambdaQuery()
                    .eq(SysChatPreserve::getUserId, loginId)
                    .likeRight(SysChatPreserve::getMsgUid, msgUid));
            final Boolean redisRemove = RedisUtils.removeDialogueRecord(loginId, msgUid);
            return ResultConvert.success(mysqlRemove);
        }
    }

    @PostMapping("/chat/history/clear")
    @ApiOperation(value = "清除对话历史记录")
    @SaIgnore
    public ResultInfo<Boolean> clearChatHistory() {
        final List<SysUser> userList = sysUserMapper.selectList(Wrappers.<SysUser>lambdaQuery());
        userList.forEach(
                item -> {
                    final Long loginId = item.getUserId();
                    final List<SysChatPreserve> list = sysChatPreserveService.list(Wrappers.<SysChatPreserve>lambdaQuery()
                            .eq(SysChatPreserve::getUserId, loginId)
                            .le(SysChatPreserve::getUpdateTime, "2025-03-20 00:00:00"));
                    for (final SysChatPreserve sysChatPreserve : list) {
                        // 删除Redis缓存和MySQL记录
                        RedisUtils.removeDialogueRecord(loginId, sysChatPreserve.getMsgUid());
                        sysChatPreserveService.remove(Wrappers.<SysChatPreserve>lambdaQuery()
                                .eq(SysChatPreserve::getUserId, loginId)
                                .likeRight(SysChatPreserve::getMsgUid, sysChatPreserve.getMsgUid()));
                    }
                }
        );
        return ResultConvert.success(true);
    }

    @GetMapping("/model/list")
    @ApiOperation(value = "问答模型列表")
    public ResultInfo<List<ModelNameValueVO>> modelList() {
        final List<ModelNameValueVO> list = new ArrayList<>(6);
        list.add(new ModelNameValueVO("reasoning-671b", "deepseek-r1", true));
        list.add(new ModelNameValueVO("reasoning", "deepseek-r1-distill-qwen-7b", true));
        list.add(new ModelNameValueVO("reasoning-14b", "deepseek-r1-distill-qwen-14b", true));
        list.add(new ModelNameValueVO("reasoning-32b", "deepseek-r1-distill-qwen-32b", true));
        list.add(new ModelNameValueVO("reasoning-70b", "deepseek-r1-distill-qwen-70b", true));
        return ResultConvert.success(list);
    }

    @PostMapping(value = "/file2text", consumes = "multipart/form-data")
    @ApiOperation(value = "解析文件为文本")
    public ResultInfo<String> analysisFileContent(@ApiParam(value = "待上传文件") final MultipartFile file) throws IOException {
        return ResultConvert.success(sysFileService.analysisFileContent(file));
    }
    
    @GetMapping("/dify/parameters/{agentKey}")
    @ApiOperation(value = "获取Dify应用参数")
    @SaCheckLogin
    public ResultInfo<DifyApplicationParametersVO> getDifyParameters(@PathVariable final String agentKey) {
        return ResultConvert.success(difyApiService.getApplicationParameters(agentKey));
    }

}
