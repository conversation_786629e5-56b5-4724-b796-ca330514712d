package com.quantchi.knowledge.center.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quantchi.knowledge.ai.client.ChatBotClient;
import com.quantchi.knowledge.ai.entity.chatmodel.ChatQueryRequest;
import com.quantchi.knowledge.ai.entity.request.ReportPromptPageRequest;
import com.quantchi.knowledge.ai.entity.response.ChatResponse;
import com.quantchi.knowledge.ai.vo.PromptTemplateConfigVO;
import com.quantchi.knowledge.center.bean.dto.ReportPolishRequest;
import com.quantchi.knowledge.center.bean.dto.ReportPolishResponse;
import com.quantchi.knowledge.center.bean.entity.PromptTemplateConfig;
import com.quantchi.knowledge.center.bean.enums.BusinessType;
import com.quantchi.knowledge.center.bean.vo.CommonDataForEsVO;
import com.quantchi.knowledge.center.common.ResultConvert;
import com.quantchi.knowledge.center.common.ResultInfo;
import com.quantchi.knowledge.center.config.ModelParamConfig;
import com.quantchi.knowledge.center.config.annotation.Log;
import com.quantchi.knowledge.center.service.IPromptTemplateConfigService;
import com.quantchi.knowledge.center.service.SysWorkBenchService;
import com.quantchi.knowledge.center.service.impl.ReportPreserveService;
import com.quantchi.knowledge.center.util.HttpServletRequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/24 10:47
 */
@RestController
@RequestMapping("/report")
@Api(tags = "智能报告接口")
@RequiredArgsConstructor
@Slf4j
public class AIReportController {

    private final SysWorkBenchService sysWorkBenchService;

    private final IPromptTemplateConfigService promptTemplateConfigService;

    private final ChatBotClient chatBotClient;

    private final ReportPreserveService reportPreserveService;

    private final ModelParamConfig modelParamConfig;

    @PostMapping("/pagePrompt")
    @ApiOperation("prompt分页列表")
    public ResultInfo<PageInfo<PromptTemplateConfigVO>> promptList(@RequestBody final ReportPromptPageRequest pageBO) {
        final String category = pageBO.getCategory();
        final Integer subType = pageBO.getSubType();
        final String keyword = pageBO.getKeyword();
        PageHelper.startPage(pageBO.getPageNum(), pageBO.getPageSize());
        final List<PromptTemplateConfig> list = promptTemplateConfigService.list(Wrappers.<PromptTemplateConfig>lambdaQuery()
                .eq(PromptTemplateConfig::getType, 2)
                .eq(CharSequenceUtil.isNotBlank(category), PromptTemplateConfig::getCategory, category)
                .eq(subType != null, PromptTemplateConfig::getSubType, subType)
                .like(CharSequenceUtil.isNotBlank(keyword), PromptTemplateConfig::getContent, keyword));
        final PageInfo<PromptTemplateConfig> configPageInfo = new PageInfo<>(list);
        final List<PromptTemplateConfigVO> collect = list.stream().map(PromptTemplateConfigVO::toVO).collect(Collectors.toList());
        final PageInfo<PromptTemplateConfigVO> resultInfo = new PageInfo<>();
        BeanUtils.copyProperties(configPageInfo, resultInfo);
        resultInfo.setList(collect);
        return ResultConvert.success(resultInfo);
    }

    @GetMapping("/categoryList")
    @ApiOperation("报告灵感种类列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "subType", value = "1提示词", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    })
    public ResultInfo<Set<String>> categoryList(@RequestParam(required = false, defaultValue = "1") final Integer subType) {
        final List<PromptTemplateConfig> list = promptTemplateConfigService.list(
                Wrappers.<PromptTemplateConfig>lambdaQuery()
                        .eq(PromptTemplateConfig::getType, 2)
                        .eq(subType != null, PromptTemplateConfig::getSubType, subType));
        return ResultConvert.success(list.stream().map(PromptTemplateConfig::getCategory).collect(Collectors.toSet()));
    }

    @ApiOperation("智能素材列表")
    @GetMapping("/collectList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型 资讯 政策 研报", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "keyword", value = "关键字", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", required = true, dataType = "Integer")
    })
    public ResultInfo<CommonDataForEsVO> collectList(@RequestParam final String type,
                                                     @RequestParam(required = false) final String keyword,
                                                     @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                     @RequestParam(required = false, defaultValue = "6") final Integer pageSize) throws IOException {
        final long userId = StpUtil.getLoginIdAsLong();
        return ResultConvert.success(sysWorkBenchService.collectList(userId, type, keyword, pageNum, pageSize));
    }

    @ApiOperation("报告里的问答")
    @PostMapping(value = "/stream/chats", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> chatContStream(@RequestBody final ChatQueryRequest chatQueryRequest) {
        return chatBotClient.chatContOfStream(chatQueryRequest.getMsg(), chatQueryRequest.getMsgUid());
    }
    
    @ApiOperation("删除报告")
    @DeleteMapping("/{id}")
    @SaCheckLogin
    @Log(title = "报告中心-删除报告", businessType = BusinessType.DELETE)
    public ResultInfo<Boolean> deleteReport(@PathVariable("id") Long id) {
        // 获取当前用户ID
        Long userId = StpUtil.getLoginIdAsLong();
        
        // 调用服务删除报告
        boolean result = reportPreserveService.deleteReport(id, userId);
        
        if (result) {
            return ResultConvert.success(true);
        } else {
            return ResultConvert.error("删除失败，请确认报告存在且您有权限删除");
        }
    }
    
    @ApiOperation("导出报告为Word文档(使用Aspose)")
    @GetMapping("/exportWord/{id}")
    public void exportReportToWordWithAspose(@PathVariable("id") Long id, HttpServletResponse response) throws IOException {
        reportPreserveService.exportReportToWordWithAspose(id, response);
    }

    @ApiOperation("报告润色/扩写/缩写")
    @PostMapping(value = "/stream/write", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter reportPolishStream(@RequestBody final ReportPolishRequest request) {
        // 设置更长的超时时间：30分钟
        final SseEmitter emitter = new SseEmitter(30 * 60 * 1000L);

        // 添加完成、超时和错误处理器
        emitter.onCompletion(() -> log.info("ReportPolish stream completed"));
        emitter.onTimeout(() -> log.info("ReportPolish stream timed out"));
        emitter.onError(e -> log.error("ReportPolish stream error", e));
        final HttpServletResponse httpServletResponse = HttpServletRequestUtil.getResponse();
        // 设置响应
        httpServletResponse.setHeader("Content-Type", "text/event-stream;charset=utf-8");
        httpServletResponse.setHeader("Pragma", "no-cache");
        httpServletResponse.setHeader("Cache-Control", "no-cache");
        httpServletResponse.setHeader("Connection", "keep-alive");
        httpServletResponse.setHeader("Keep-Alive", "timeout=600");
        httpServletResponse.setHeader("Content-Encoding", "none");
        httpServletResponse.setHeader("X-Accel-Buffering", "no");

        // 使用更高优先级的线程执行
        CompletableFuture.runAsync(() -> {
            try {
                // 转换请求为JSON
                String requestJson = JSON.toJSONString(request);
                log.info("Achievement correlation request: {}", requestJson);

                // 创建HTTP客户端并配置
                try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                    HttpPost httpPost = new HttpPost(modelParamConfig.getDpReportWriteUrl());

                    // 设置请求头和请求体
                    httpPost.setHeader("Content-Type", "application/json");
                    httpPost.setHeader("Accept", "text/event-stream");

                    RequestConfig requestConfig = RequestConfig.custom()
                            .setConnectTimeout(600000)
                            .setSocketTimeout(600000)
                            .build();
                    httpPost.setConfig(requestConfig);
                    httpPost.setEntity(new StringEntity(requestJson, ContentType.APPLICATION_JSON));

                    // 执行请求并获取响应流
                    try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                        int status = response.getStatusLine().getStatusCode();
                        if (status >= 200 && status < 300) {
                            HttpEntity entity = response.getEntity();
                            if (entity != null) {
                                try (BufferedReader reader = new BufferedReader(
                                        new InputStreamReader(entity.getContent(), StandardCharsets.UTF_8), 8192)) { // 使用更大的缓冲区

                                    String line;
                                    boolean isComplete = false;

                                    // 用于累积SSE事件的数据
                                    StringBuilder eventData = new StringBuilder();
                                    boolean inEvent = false;

                                    while (!isComplete && (line = reader.readLine()) != null) {
                                        // 处理SSE格式的数据
                                        final String currentLine = line; // 为lambda捕获

                                        // 空行表示事件结束
                                        if (currentLine.isEmpty()) {
                                            if (inEvent && eventData.length() > 0) {
                                                // 处理完整的事件数据
                                                String data = eventData.toString().trim();
                                                processEventData(data, emitter);

                                                // 检查是否是结束标记
                                                if (data.contains("[DONE]")) {
                                                    isComplete = true;
                                                    ReportPolishResponse endResponse = new ReportPolishResponse();
                                                    endResponse.setIsEnd(true);
                                                    emitter.send(endResponse);
                                                }

                                                // 重置事件数据
                                                eventData = new StringBuilder();
                                                inEvent = false;
                                            }
                                            continue;
                                        }

                                        // 处理事件行
                                        if (currentLine.startsWith("data:")) {
                                            String data = currentLine.substring(5).trim();
                                            inEvent = true;
                                            eventData.append(data);

                                            // 直接处理[DONE]标记
                                            if ("[DONE]".equals(data)) {
                                                isComplete = true;
                                                ReportPolishResponse endResponse = new ReportPolishResponse();
                                                endResponse.setIsEnd(true);
                                                emitter.send(endResponse);
                                                break;
                                            }

                                            // 尝试立即处理数据（如果是完整的JSON）
                                            try {
                                                ReportPolishResponse correlationResponse = JSON.parseObject(data, ReportPolishResponse.class);
                                                correlationResponse.setIsEnd(false);
                                                emitter.send(correlationResponse);
                                                // 发送后清空缓冲区
                                                eventData = new StringBuilder();
                                                inEvent = false;
                                            } catch (Exception e) {
                                                // JSON不完整，继续累积数据
                                                log.debug("Incomplete JSON data, continuing to accumulate: {}", data);
                                            }
                                        } else if (currentLine.startsWith(":")){  // 注释行
                                            // 忽略注释
                                            continue;
                                        } else if (currentLine.startsWith("event:") || currentLine.startsWith("id:") || currentLine.startsWith("retry:")) {
                                            // 处理其他SSE字段
                                            inEvent = true;
                                            continue;
                                        } else {
                                            // 其他未知行，可能是数据的一部分
                                            if (inEvent) {
                                                eventData.append(currentLine);
                                            }
                                        }
                                    }

                                    // 处理可能剩余的事件数据
                                    if (!isComplete && eventData.length() > 0) {
                                        try {
                                            String data = eventData.toString().trim();
                                            processEventData(data, emitter);
                                        } catch (Exception e) {
                                            log.warn("Failed to process remaining event data", e);
                                        }
                                    }

                                    if (!isComplete) {
                                        // 如果没有收到[DONE]标记，发送结束消息
                                        ReportPolishResponse endResponse = new ReportPolishResponse();
                                        endResponse.setIsEnd(true);
                                        emitter.send(endResponse);
                                    }
                                }
                            }
                        } else {
                            // 处理HTTP错误
                            ReportPolishResponse errorResponse = new ReportPolishResponse();
                            errorResponse.setReasoning_content("HTTP error: " + status);
                            errorResponse.setIsEnd(true);
                            emitter.send(errorResponse);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error in achievement correlation stream", e);
                try {
                    ReportPolishResponse errorResponse = new ReportPolishResponse();
                    errorResponse.setReasoning_content("Error processing request: " + e.getMessage());
                    errorResponse.setIsEnd(true);
                    emitter.send(errorResponse);
                    emitter.complete();
                } catch (IOException ex) {
                    log.error("Error sending error response", ex);
                }
            } finally {
                // 确保在所有情况下都完成emitter
                try {
                    emitter.complete();
                } catch (Exception ex) {
                    log.error("Error completing emitter", ex);
                }
            }
        });

        return emitter;
    }

    /**
     * 处理SSE事件数据
     * 尝试解析数据并发送到客户端
     *
     * @param data    事件数据
     * @param emitter SSE发射器
     * @throws IOException 如果发送失败
     */
    private void processEventData(String data, SseEmitter emitter) throws IOException {
        if (data == null || data.isEmpty()) {
            return;
        }

        // 检查是否是结束标记
        if ("[DONE]".equals(data)) {
            ReportPolishResponse endResponse = new ReportPolishResponse();
            endResponse.setIsEnd(true);
            emitter.send(endResponse);
            return;
        }

        try {
            // 尝试解析JSON数据
            ReportPolishResponse correlationResponse = JSON.parseObject(data, ReportPolishResponse.class);
            correlationResponse.setIsEnd(false);
            emitter.send(correlationResponse);
        } catch (Exception e) {
            // 如果不是有效的JSON，尝试发送原始数据
            log.warn("Failed to parse JSON data: {}", data, e);

            // 创建一个简单的响应对象包含原始数据
            ReportPolishResponse fallbackResponse = new ReportPolishResponse();
            fallbackResponse.setReasoning_content(data);
            fallbackResponse.setIsEnd(false);
            emitter.send(fallbackResponse);
        }
    }
}
