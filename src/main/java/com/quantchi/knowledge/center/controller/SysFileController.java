package com.quantchi.knowledge.center.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.io.IoUtil;

import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.vo.OSSPresignedVO;
import com.quantchi.knowledge.center.bean.vo.SysFileVO;
import com.quantchi.knowledge.center.common.ResultConvert;
import com.quantchi.knowledge.center.common.ResultInfo;
import com.quantchi.knowledge.center.config.properties.AliyunProperties;
import com.quantchi.knowledge.center.service.ISysFileService;
import com.quantchi.knowledge.center.service.IFileStorageService;
import com.quantchi.knowledge.center.util.LocalFileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

import java.net.URLEncoder;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 文件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@RestController
@RequestMapping("/file")
@Api(tags = "文件控制器")
@Slf4j
public class SysFileController {

    public static final String FILE_MODULE_NAME = "icirtest";

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private AliyunProperties ossProperties;

    @Autowired
    private IFileStorageService fileStorageService;

    public static final String CHAT_DOC_TYPE = "chat_doc";

    public static final String REPORT_TYPE = "report";

    @PostMapping(value = "/upload", consumes = "multipart/form-data")
    @ApiOperation(value = "上传文件")
    public ResultInfo<SysFileVO> uploadFile(@ApiParam(value = "待上传文件") @RequestParam final MultipartFile file,
                                            @RequestParam(required = false) final String fileType) throws Exception {
        return ResultConvert.success(sysFileService.uploadFile(file, fileType));
    }

    @PostMapping(value = "/uploadBlob", consumes = "multipart/form-data")
    @ApiOperation(value = "上传blob文件")
    public ResultInfo<SysFileVO> uploadBlobFile(@ApiParam(value = "待上传文件") final MultipartFile file) throws Exception {
        return ResultConvert.success(sysFileService.uploadBlobFile(file));
    }

    /**
     * 文件下载
     *
     * @param fileName
     * @param response
     */
    @GetMapping("/download")
    @ApiOperation("下载文件")
    @ResponseBody
    @SaIgnore
    public void downloadFile(@RequestParam(name = "fileName") final String fileName,
                             @RequestParam(name = "originFileName") final String originFileName,
                             @RequestParam(name = "reportId", required = false) final Long reportId,
                             @RequestParam(name = "token", required = false) final String token,
                             @RequestParam(required = false, defaultValue = "false") final Boolean preview,
                             final HttpServletResponse response) {
        sysFileService.downloadFile(fileName, originFileName, preview, reportId, token, response);
    }

    @GetMapping("/local/download")
    @ApiOperation("本地文件下载链接")
    @ResponseBody
    public void downloadFile(@RequestParam(name = "fileName") final String fileName,
                             @RequestParam(required = false, defaultValue = "false") final Boolean preview,
                             final HttpServletResponse response) {
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            final byte[] buffer;
            inputStream = getInputStreamFromLocal(fileName);
            buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            response.reset();
            // 设置response的Header
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "*");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Cache-Control", "public, max-age=" + TimeUnit.DAYS.toSeconds(30));
            response.setHeader("Expires", new Date(System.currentTimeMillis() + TimeUnit.DAYS.toMillis(30)).toString());
            if (fileName.contains("png")) {
                response.setContentType(MediaType.IMAGE_PNG_VALUE);
            } else if (fileName.contains("jpg")) {
                response.setContentType(MediaType.IMAGE_JPEG_VALUE);
            } else {
                //attachment表示以附件方式下载   inline表示在线打开   "Content-Disposition: inline; filename=文件名.mp3"
                if (preview) {
                    response.addHeader("Content-Disposition", "inline; filename=" + URLEncoder.encode(fileName, "UTF-8"));
                    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
                } else {
                    response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
                    response.setContentType("application/octet-stream");
                }
            }
            // 文件的大小
            outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
        } catch (final Exception e) {
            log.error("下载文件异常，文件名{}", fileName, e);
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(outputStream);
        }
    }

    private InputStream getInputStreamFromLocal(final String fileName) throws IOException {
        final Resource resource = new ClassPathResource("picture/" + fileName);
        final InputStream inputStream = resource.getInputStream();
        // 将文件写入输入流
        return new BufferedInputStream(inputStream);
    }

    @DeleteMapping("/file")
    @ResponseBody
    @ApiOperation("删除文件")
    public ResultInfo<Boolean> deleteFile(@RequestParam(name = "fileName") final String fileName) {
        return ResultConvert.success(sysFileService.deleteFile(fileName));
    }

    @GetMapping("/getPresignedUrl")
    @ResponseBody
    @ApiOperation("服务端生成PutObject所需的签名URL")
    public ResultInfo<OSSPresignedVO> generatePresignedURL(@RequestParam(required = false) final String fileName) {
        final String fileExtension = LocalFileUtil.getFileExtension(fileName);
        final String storageFileName = LocalFileUtil.getStorageFileName(FILE_MODULE_NAME, fileExtension);
        try {
            final String bucketName = ossProperties.getBucketName();

            // 阿里云OSS要求使用UTC时间生成预签名URL
            // 过期时间20分钟
            final Date expiration = new Date(System.currentTimeMillis() + 20 * 60 * 1000L);

            // 记录时间信息用于调试
            final Calendar cstCalendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
            cstCalendar.setTime(expiration);
            log.info("生成预签名URL，过期时间戳: {}, UTC时间: {}, CST时间: {}",
                    expiration.getTime(), expiration, cstCalendar.getTime());

            // 确定内容类型
            final String contentType = determineContentType(fileExtension);
            
            // 使用文件存储服务生成签名URL
            final String urlString = fileStorageService.generatePresignedUrl(bucketName, storageFileName, contentType, expiration);
            
            final OSSPresignedVO vo = new OSSPresignedVO(urlString, storageFileName);
            return ResultConvert.success(vo);
        } catch (final Exception e) {
            log.error("生成PresignedUrl失败", e);
            throw new BusinessException("生成PresignedUrl失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据文件扩展名确定内容类型
     * 
     * @param fileExtension 文件扩展名
     * @return 内容类型
     */
    private String determineContentType(final String fileExtension) {
        switch (fileExtension) {
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            default:
                throw new BusinessException("不支持的文件类型");
        }
    }

}
