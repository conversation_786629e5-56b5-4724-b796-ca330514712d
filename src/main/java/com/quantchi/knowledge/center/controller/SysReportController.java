package com.quantchi.knowledge.center.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.github.pagehelper.PageInfo;
import com.quantchi.knowledge.center.bean.enums.BusinessType;
import com.quantchi.knowledge.center.bean.enums.ReportThemeEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.system.bo.*;
import com.quantchi.knowledge.center.bean.system.query.ReportQuery;
import com.quantchi.knowledge.center.bean.system.vo.*;
import com.quantchi.knowledge.center.bean.vo.TocEntryVO;
import com.quantchi.knowledge.center.common.ResultConvert;
import com.quantchi.knowledge.center.common.ResultInfo;
import com.quantchi.knowledge.center.config.annotation.Log;
import com.quantchi.knowledge.center.service.IMaterialSearchService;
import com.quantchi.knowledge.center.service.ISysAiReportService;
import com.quantchi.knowledge.center.service.ISysReportService;
import com.quantchi.knowledge.center.service.ISysUserApiUsageService;
import com.quantchi.knowledge.center.util.HttpServletRequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 报告表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/report")
@Api(tags = "报告中心接口")
public class SysReportController {

    private final ISysReportService sysReportService;
    private final ISysUserApiUsageService sysUserApiUsageService;
    private final ISysAiReportService sysAiReportService;
    private final IMaterialSearchService materialSearchService;

    @PostMapping("/upload")
    @ApiOperation("上传报告")
    @Log(title = "工作台-我的报告", businessType = BusinessType.UPLOAD)
    public ResultInfo<Boolean> uploadReport(@Valid @RequestBody final SysReportUploadBO reportUploadBO) throws Exception {
        return ResultConvert.success(sysReportService.uploadReport(reportUploadBO));
    }

    @PostMapping("/myReportList")
    @ApiOperation("我的报告列表")
    public ResultInfo<PageInfo<SysReportPageVO>> myReportList(@RequestBody final ReportQuery reportQuery) {
        return ResultConvert.success(sysReportService.myReportList(reportQuery));
    }

    @PostMapping("/reportMarket")
    @ApiOperation("报告集市")
    @Log(title = "报告中心-报告集市", businessType = BusinessType.VIEW)
    public ResultInfo<PageInfo<SysReportPageVO>> reportMarket(@RequestBody final ReportQuery reportQuery) {
        return ResultConvert.success(sysReportService.reportMarket(reportQuery));
    }

    @PostMapping("/public")
    @ApiOperation("公开报告")
    public ResultInfo<Boolean> publicReport(@RequestParam final Long reportId) {
        return ResultConvert.success(sysReportService.publicReport(reportId));
    }

    @PostMapping("/unShelf")
    @ApiOperation("下架报告")
    public ResultInfo<Boolean> unShelfReport(@RequestParam final Long reportId) {
        return ResultConvert.success(sysReportService.unShelfReport(reportId));
    }

    @PostMapping("/delete")
    @ApiOperation("删除报告")
    @Log(title = "删除报告", businessType = BusinessType.DELETE)
    public ResultInfo<Boolean> deleteReport(@RequestParam final Long reportId) {
        return ResultConvert.success(sysReportService.deleteReport(reportId));
    }

    @PostMapping("/batchDelete")
    @ApiOperation("批量删除报告")
    @Log(title = "批量删除报告", businessType = BusinessType.DELETE)
    public ResultInfo<SysReportBatchDeleteResultVO> batchDeleteReport(@Valid @RequestBody final SysReportBatchDeleteBO batchDeleteBO) {
        return ResultConvert.success(sysReportService.batchDeleteReport(batchDeleteBO));
    }

    @PostMapping("/info")
    @ApiOperation("报告详情")
    @SaCheckLogin
    public ResultInfo<SysReportDetailVO> reportInfo(@RequestParam final Long reportId) {
        Long userId = StpUtil.getLoginIdAsLong();
        
        // 检查用户是否有权限查看报告
        boolean canUseApi = sysUserApiUsageService.checkUserCanUseApi(userId, ISysUserApiUsageService.API_TYPE_REPORT);
        if (!canUseApi) {
            throw new BusinessException("您的免费查看报告次数已用完，请升级为正式账号继续使用");
        }
        
        // 记录API使用情况
        sysUserApiUsageService.recordApiUsage(userId, ISysUserApiUsageService.API_TYPE_REPORT);
        return ResultConvert.success(sysReportService.reportInfo(reportId));
    }
    
    @GetMapping("/usage/remaining")
    @ApiOperation("获取报告查看剩余次数")
    @SaCheckLogin
    public ResultInfo<Integer> getRemainingReportUsage() {
        Long userId = StpUtil.getLoginIdAsLong();
        int remainingCount = sysUserApiUsageService.getRemainingUsageCount(userId, ISysUserApiUsageService.API_TYPE_REPORT);
        return ResultConvert.success(remainingCount);
    }

    @PostMapping("/submit")
    @ApiOperation("提交报告")
    @Log(title = "提交报告", businessType = BusinessType.UPDATE)
    public ResultInfo<Boolean> submitReport(@Valid @RequestBody final SysReportSubmitBO reportSubmitBO) {
        return ResultConvert.success(sysReportService.submitReport(reportSubmitBO));
    }

    @PostMapping("/download")
    @ApiOperation("下载报告")
    @Log(title = "报告中心-报告下载", businessType = BusinessType.DOWNLOAD)
    public ResultInfo<Integer> downloadReport(@Valid @RequestBody final SysReportDownloadBO reportDownloadBO) throws Exception {
        return ResultConvert.success(sysReportService.downloadReport(reportDownloadBO));
    }

    // ==================== AI报告相关接口 ====================

    @PostMapping("/ai/generateOutline")
    @ApiOperation("生成报告大纲")
    public ResultInfo<List<TocEntryVO>> generateOutline(@Valid @RequestBody final AiReportCreateBO createBO) {
        return ResultConvert.success(sysAiReportService.generateOutline(createBO));
    }

    @PostMapping("/ai/generate")
    @ApiOperation("生成AI报告")
    public ResultInfo<String> generateAiReport(@Valid @RequestBody final AiReportGenerateBO generateBO) {
        return ResultConvert.success(sysAiReportService.generateAiReport(generateBO));
    }

    @PostMapping(value = "/ai/generateStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("流式生成AI报告")
    @SaCheckLogin
    public Flux<ServerSentEvent<AiReportStreamResponse>> generateAiReportStream(@Valid @RequestBody final AiReportGenerateBO generateBO) {
        log.info("开始流式生成AI报告，标题: {}", generateBO.getTitle());
        final HttpServletResponse httpServletResponse = HttpServletRequestUtil.getResponse();
        // 设置必要的响应头，确保流式传输工作正常
        httpServletResponse.setHeader("Cache-Control", "no-cache");
        httpServletResponse.setHeader("Connection", "keep-alive");
        httpServletResponse.setHeader("Keep-Alive", "timeout=600");
        httpServletResponse.setHeader("Content-Encoding", "none");
        httpServletResponse.setHeader("X-Accel-Buffering", "no"); // 禁用nginx缓冲

        return sysAiReportService.generateAiReportStream(generateBO)
                .doOnSubscribe(subscription -> log.info("客户端订阅流式响应"))
                .doOnNext(response -> log.debug("准备发送响应: event={}, answer长度={}",
                         response.getEvent(),
                         response.getAnswer() != null ? response.getAnswer().length() : 0))
                .map(response -> {
                    ServerSentEvent<AiReportStreamResponse> sse = ServerSentEvent.<AiReportStreamResponse>builder()
                            .data(response)
                            .event(response.getEvent())
                            .id(response.getMessageId())
                            .build();
                    log.debug("创建SSE事件: event={}, id={}", response.getEvent(), response.getMessageId());
                    return sse;
                })
                .doOnNext(sse -> log.debug("发送SSE事件到客户端: event={}", sse.event()))
                .doOnComplete(() -> log.info("流式响应完成"))
                .doOnError(error -> log.error("流式响应错误", error))
                .doOnCancel(() -> log.warn("客户端取消了流式响应"));
    }

    @GetMapping("/ai/detail/{reportId}")
    @ApiOperation("获取AI报告详情")
    public ResultInfo<AiReportDetailVO> getAiReportDetail(@PathVariable final Long reportId) {
        return ResultConvert.success(sysAiReportService.getAiReportDetail(reportId));
    }

    @ApiOperation("重命名报告")
    @PutMapping("/ai/rename/{id}")
    @SaCheckLogin
    @Log(title = "报告中心-重命名报告", businessType = BusinessType.UPDATE)
    public ResultInfo<Boolean> renameReport(
            @PathVariable("id") Long id,
            @RequestParam("newTitle") String newTitle) {
        // 参数校验
        if (CharSequenceUtil.isBlank(newTitle)) {
            return ResultConvert.error("新标题不能为空");
        }

        // 获取当前用户ID
        Long userId = StpUtil.getLoginIdAsLong();

        try {
            // 调用服务重命名报告
            boolean result = sysReportService.renameReport(id, newTitle, userId);

            if (result) {
                return ResultConvert.success(true);
            } else {
                return ResultConvert.error("重命名失败，请确认报告存在且您有权限修改");
            }
        } catch (IllegalArgumentException e) {
            // 捕获标题重复异常
            return ResultConvert.error(e.getMessage());
        } catch (Exception e) {
            log.error("重命名报告异常", e);
            return ResultConvert.error("重命名失败：" + e.getMessage());
        }
    }

    @GetMapping("/ai/themes")
    @ApiOperation("获取AI报告主题选项")
    public ResultInfo<String[]> getReportThemes() {
        return ResultConvert.success(ReportThemeEnum.getAllThemes());
    }

    @PostMapping("/ai/materialSearch")
    @ApiOperation("素材检索")
    public ResultInfo<List<MaterialItemVO>> materialSearch(@Valid @RequestBody final MaterialSearchBO searchBO) {
        return ResultConvert.success(materialSearchService.materialSearch(searchBO));
    }

    @PostMapping("/ai/save")
    @ApiOperation("保存报告编辑内容")
    @Log(title = "保存报告编辑内容", businessType = BusinessType.UPDATE)
    public ResultInfo<Boolean> saveReport(@Valid @RequestBody final SysReportSaveBO saveBO) {
        return ResultConvert.success(sysAiReportService.saveReport(saveBO));
    }

}
