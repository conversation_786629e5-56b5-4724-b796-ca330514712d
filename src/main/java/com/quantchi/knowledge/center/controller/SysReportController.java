package com.quantchi.knowledge.center.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.github.pagehelper.PageInfo;
import com.quantchi.knowledge.center.bean.enums.BusinessType;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.system.bo.SysReportUploadBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportSubmitBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportDownloadBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportBatchDeleteBO;
import com.quantchi.knowledge.center.bean.system.query.ReportQuery;
import com.quantchi.knowledge.center.bean.system.vo.SysReportDetailVO;
import com.quantchi.knowledge.center.bean.system.vo.SysReportPageVO;
import com.quantchi.knowledge.center.bean.system.vo.SysReportBatchDeleteResultVO;
import com.quantchi.knowledge.center.common.ResultConvert;
import com.quantchi.knowledge.center.common.ResultInfo;
import com.quantchi.knowledge.center.config.annotation.Log;
import com.quantchi.knowledge.center.service.ISysReportService;
import com.quantchi.knowledge.center.service.ISysUserApiUsageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 报告表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/report")
@Api(tags = "报告中心接口")
public class SysReportController {

    private final ISysReportService sysReportService;
    private final ISysUserApiUsageService sysUserApiUsageService;

    @PostMapping("/upload")
    @ApiOperation("上传报告")
    @Log(title = "工作台-我的报告", businessType = BusinessType.UPLOAD)
    public ResultInfo<Boolean> uploadReport(@Valid @RequestBody final SysReportUploadBO reportUploadBO) throws Exception {
        return ResultConvert.success(sysReportService.uploadReport(reportUploadBO));
    }

    @PostMapping("/myReportList")
    @ApiOperation("我的报告列表")
    public ResultInfo<PageInfo<SysReportPageVO>> myReportList(@RequestBody final ReportQuery reportQuery) {
        return ResultConvert.success(sysReportService.myReportList(reportQuery));
    }

    @PostMapping("/reportMarket")
    @ApiOperation("报告集市")
    @Log(title = "报告中心-报告集市", businessType = BusinessType.VIEW)
    public ResultInfo<PageInfo<SysReportPageVO>> reportMarket(@RequestBody final ReportQuery reportQuery) {
        return ResultConvert.success(sysReportService.reportMarket(reportQuery));
    }

    @PostMapping("/public")
    @ApiOperation("公开报告")
    public ResultInfo<Boolean> publicReport(@RequestParam final Long reportId) {
        return ResultConvert.success(sysReportService.publicReport(reportId));
    }

    @PostMapping("/unShelf")
    @ApiOperation("下架报告")
    public ResultInfo<Boolean> unShelfReport(@RequestParam final Long reportId) {
        return ResultConvert.success(sysReportService.unShelfReport(reportId));
    }

    @PostMapping("/delete")
    @ApiOperation("删除报告")
    @Log(title = "工作台-我的报告", businessType = BusinessType.DELETE)
    public ResultInfo<Boolean> deleteReport(@RequestParam final Long reportId) {
        return ResultConvert.success(sysReportService.deleteReport(reportId));
    }

    @PostMapping("/batchDelete")
    @ApiOperation("批量删除报告")
    @Log(title = "工作台-我的报告", businessType = BusinessType.DELETE)
    public ResultInfo<SysReportBatchDeleteResultVO> batchDeleteReport(@Valid @RequestBody final SysReportBatchDeleteBO batchDeleteBO) {
        return ResultConvert.success(sysReportService.batchDeleteReport(batchDeleteBO));
    }

    @PostMapping("/info")
    @ApiOperation("报告详情")
    @SaCheckLogin
    public ResultInfo<SysReportDetailVO> reportInfo(@RequestParam final Long reportId) {
        Long userId = StpUtil.getLoginIdAsLong();
        
        // 检查用户是否有权限查看报告
        boolean canUseApi = sysUserApiUsageService.checkUserCanUseApi(userId, ISysUserApiUsageService.API_TYPE_REPORT);
        if (!canUseApi) {
            throw new BusinessException("您的免费查看报告次数已用完，请升级为正式账号继续使用");
        }
        
        // 记录API使用情况
        sysUserApiUsageService.recordApiUsage(userId, ISysUserApiUsageService.API_TYPE_REPORT);
        return ResultConvert.success(sysReportService.reportInfo(reportId));
    }
    
    @GetMapping("/usage/remaining")
    @ApiOperation("获取报告查看剩余次数")
    @SaCheckLogin
    public ResultInfo<Integer> getRemainingReportUsage() {
        Long userId = StpUtil.getLoginIdAsLong();
        int remainingCount = sysUserApiUsageService.getRemainingUsageCount(userId, ISysUserApiUsageService.API_TYPE_REPORT);
        return ResultConvert.success(remainingCount);
    }

    @PostMapping("/submit")
    @ApiOperation("提交报告")
    @Log(title = "工作台-我的报告", businessType = BusinessType.UPDATE)
    public ResultInfo<Boolean> submitReport(@Valid @RequestBody final SysReportSubmitBO reportSubmitBO) {
        return ResultConvert.success(sysReportService.submitReport(reportSubmitBO));
    }

    @PostMapping("/download")
    @ApiOperation("下载报告")
    @Log(title = "报告中心-报告下载", businessType = BusinessType.DOWNLOAD)
    public ResultInfo<Integer> downloadReport(@Valid @RequestBody final SysReportDownloadBO reportDownloadBO) throws Exception {
        return ResultConvert.success(sysReportService.downloadReport(reportDownloadBO));
    }

}
