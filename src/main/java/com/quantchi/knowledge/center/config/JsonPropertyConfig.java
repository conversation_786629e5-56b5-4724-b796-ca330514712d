package com.quantchi.knowledge.center.config;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.quantchi.knowledge.center.bean.entity.DmIpc;
import com.quantchi.knowledge.center.bean.model.NavigationSettings;
import com.quantchi.knowledge.center.config.properties.KeywordSearchProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * json文件格式配置读取和注册对应的bean
 *
 * <AUTHOR>
 * @date 2022/4/20 14:12
 */
@Configuration
@Slf4j
public class JsonPropertyConfig {

    /**
     * 自定义多格式日期反序列化器
     * 支持多种日期格式的解析：yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd
     */
    public static class MultiFormatDateDeserializer extends JsonDeserializer<Date> {
        private final SimpleDateFormat[] dateFormats = {
            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"),
            new SimpleDateFormat("yyyy-MM-dd")
        };

        public MultiFormatDateDeserializer() {
            // 设置所有格式的时区为中国标准时间
            for (SimpleDateFormat format : dateFormats) {
                format.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
            }
        }

        @Override
        public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
            final String dateString = jsonParser.getText();
            if (dateString == null || dateString.trim().isEmpty()) {
                return null;
            }

            // 尝试用不同的格式解析日期
            for (SimpleDateFormat format : dateFormats) {
                try {
                    synchronized (format) { // SimpleDateFormat不是线程安全的
                        return format.parse(dateString.trim());
                    }
                } catch (ParseException e) {
                    // 继续尝试下一个格式
                }
            }

            // 如果所有格式都失败，抛出异常
            throw new IOException("无法解析日期字符串: " + dateString +
                ", 支持的格式: yyyy-MM-dd HH:mm:ss, yyyy-MM-dd");
        }
    }

    @Bean
    public NavigationSettings navigationSettings() throws IOException {
        final Resource resource = new ClassPathResource("navigation_setting.json");
        final String jsonStr = readResourceAsString(resource);
        return JSONObject.parseObject(jsonStr, NavigationSettings.class);
    }

    @Bean
    public KeywordSearchProperties giksKeywordSearch() throws IOException {
        final Resource resource = new ClassPathResource("keyword_search.json");
        final String jsonStr = readResourceAsString(resource);
        return JSONObject.parseObject(jsonStr, KeywordSearchProperties.class);
    }

    @Bean
    public List<DmIpc> ipcList() throws IOException {
        final Resource resource = new ClassPathResource("dm_ipc.json");
        final String jsonStr = readResourceAsString(resource);
        return JSONObject.parseArray(jsonStr, DmIpc.class);
    }

    /**
     * 配置Jackson ObjectMapper，统一设置时区为中国标准时间
     * 解决时区不一致导致的时间差问题，并支持Java 8时间类型
     *
     * 支持多种Date格式的自动解析：
     * - yyyy-MM-dd HH:mm:ss（完整日期时间格式）
     * - yyyy-MM-dd（仅日期格式）
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        final ObjectMapper mapper = new ObjectMapper();

        // 设置时区为中国标准时间
        mapper.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));

        // 注册Java 8时间模块
        final JavaTimeModule javaTimeModule = new JavaTimeModule();

        // 配置LocalDate序列化和反序列化格式
        final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));

        // 配置LocalDateTime序列化和反序列化格式
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));

        mapper.registerModule(javaTimeModule);

        // 创建自定义模块来处理多格式Date反序列化
        final SimpleModule dateModule = new SimpleModule();
        dateModule.addDeserializer(Date.class, new MultiFormatDateDeserializer());
        mapper.registerModule(dateModule);

        // 设置传统Date类型的序列化格式（默认使用完整格式）
        final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        mapper.setDateFormat(dateFormat);

        // 配置忽略未知属性，避免JSON中多余字段导致反序列化失败
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        log.info("Jackson ObjectMapper configured with timezone: Asia/Shanghai, Java 8 time support, and multi-format Date parsing");

        return mapper;
    }


    /**
     * 将资源加载为一个字符串
     *
     * @param resource
     * @return
     * @throws IOException
     */
    private String readResourceAsString(final Resource resource) throws IOException {
        final StringBuilder stringBuilder = new StringBuilder();
        final InputStream in = resource.getInputStream();
        final InputStreamReader inputStreamReader = new InputStreamReader(in);
        final BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
        try {
            String line;
            while (((line = bufferedReader.readLine()) != null)) {
                stringBuilder.append(line);
            }
            return stringBuilder.toString();
        } finally {
            bufferedReader.close();
            inputStreamReader.close();
            in.close();
        }
    }
}