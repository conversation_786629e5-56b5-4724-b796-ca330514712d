package com.quantchi.knowledge.center.config;

import com.quantchi.knowledge.ai.client.ChatBotClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 */
@Data
@Slf4j
@Configuration
@ConfigurationProperties("model.param")
public class ModelParamConfig {

    private String oneApiUrl;
    private String dpKeywordUrl;
    private String dpAccessToken;
    private String dpReportWriteUrl;

    @Bean
    public ChatBotClient ernieBotClient() {
        return new ChatBotClient(oneApiUrl) {
            @Override
            public String getAccessToken() {
                return "Bearer " + dpAccessToken;
            }
        };
    }

}
