package com.quantchi.knowledge.center.bean.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 产业链报告VO
 *
 * <AUTHOR>
 * @date 2025/03/19
 */
@Data
@ApiModel("产业链报告VO")
public class IndustryChainReportVO {

    @ApiModelProperty("报告内容（Markdown格式）")
    private String content;

    @ApiModelProperty("目录列表")
    private List<TocEntryVO> tocEntries;

    /**
     * 目录项VO
     */
    @Data
    @ApiModel("目录项VO")
    public static class TocEntryVO {
        @ApiModelProperty("标题")
        private String title;
        
        @ApiModelProperty("层级")
        private int level;
        
        @ApiModelProperty("锚点ID")
        private String anchorId;
    }
}
