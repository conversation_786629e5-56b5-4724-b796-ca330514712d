package com.quantchi.knowledge.center.bean.dto;

import com.quantchi.knowledge.center.bean.vo.IndustryChainReportVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 产业链报告内容请求DTO
 *
 * <AUTHOR>
 * @date 2025/03/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("产业链报告内容请求DTO")
public class IndustryChainReportContentDTO extends IndustryChainReportDTO {

    @ApiModelProperty("用户修改后的目录列表")
    private List<IndustryChainReportVO.TocEntryVO> tocEntries;
}
