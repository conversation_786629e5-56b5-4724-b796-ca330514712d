package com.quantchi.knowledge.center.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报告主题枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum ReportThemeEnum {

    INDUSTRY("产业类", "产业类报告", "industry"),
//    TECHNOLOGY("技术类", "技术类报告", "technology"),
    ENTERPRISE("企业类", "企业类报告", "enterprise"),
    INVESTMENT("招商类", "招商类报告", "investment");
//    OTHER("其他", "其他类型报告", "other");

    private final String code;
    private final String desc;
    private final String apiType;

    /**
     * 根据代码获取枚举
     */
    public static ReportThemeEnum getByCode(final String code) {
        if (code == null) {
            return null;
        }
        for (final ReportThemeEnum themeEnum : values()) {
            if (themeEnum.getCode().equals(code)) {
                return themeEnum;
            }
        }
        return null;
    }

    /**
     * 根据代码获取描述
     */
    public static String getDescByCode(final String code) {
        final ReportThemeEnum themeEnum = getByCode(code);
        return themeEnum != null ? themeEnum.getDesc() : "未知主题";
    }

    /**
     * 根据代码获取API类型
     */
    public static String getApiTypeByCode(final String code) {
        final ReportThemeEnum themeEnum = getByCode(code);
        return themeEnum != null ? themeEnum.getApiType() : "industry";
    }

    /**
     * 获取所有主题选项
     */
    public static String[] getAllThemes() {
        final ReportThemeEnum[] values = values();
        final String[] themes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            themes[i] = values[i].getCode();
        }
        return themes;
    }
}
