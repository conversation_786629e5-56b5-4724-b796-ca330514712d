package com.quantchi.knowledge.center.bean.bo;

import com.quantchi.knowledge.center.bean.vo.IndustryChainReportVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 报告数据保存
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "报告保存对象", description = "报告数据保存")
public class ReportPreserveBO {

    @ApiModelProperty(value = "报告id", required = true)
    private Long reportId;

    @ApiModelProperty(value = "产业链ID", required = true)
    private String chainId;

    @ApiModelProperty("报告标题")
    private String title;

    @ApiModelProperty("关键词列表")
    private List<String> keywords;

    @ApiModelProperty("用户修改后的目录列表")
    private List<IndustryChainReportVO.TocEntryVO> tocEntries;

    @ApiModelProperty("保存数据")
    private String preserveData;

}
