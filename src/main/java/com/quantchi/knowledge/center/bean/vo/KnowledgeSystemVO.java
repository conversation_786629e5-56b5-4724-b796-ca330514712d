package com.quantchi.knowledge.center.bean.vo;


import com.quantchi.knowledge.center.bean.model.NodeData;
import com.quantchi.knowledge.center.bean.model.Sankey;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class KnowledgeSystemVO {

    @ApiModelProperty("链图id")
    private String id;

    @ApiModelProperty("链图名称")
    private String name;

    @ApiModelProperty("知识领域英文")
    private String nameEn;

    @ApiModelProperty("链图简介")
    private String description;

    @ApiModelProperty("节点数量")
    private Integer nodeCount;

    @ApiModelProperty("企业数量")
    private Long companyCount;

    @ApiModelProperty("技术数量")
    private Long techCount;

    @ApiModelProperty("专利数量")
    private Integer patentCount;

    @ApiModelProperty("节点数据")
    private NodeData nodeData;

    @ApiModelProperty("桑基图数据")
    private Sankey sankey;

}
