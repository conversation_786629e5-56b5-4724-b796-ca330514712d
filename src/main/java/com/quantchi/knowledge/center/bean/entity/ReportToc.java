package com.quantchi.knowledge.center.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 报告目录数据保存
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("report_toc")
@ApiModel(value = "ReportToc对象", description = "报告目录数据保存")
public class ReportToc implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("层级")
    private Byte level;

    @ApiModelProperty("锚点ID")
    private String anchorId;

    @ApiModelProperty("关联的报告id")
    private Long reportId;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String TITLE = "title";

    public static final String LEVEL = "level";

    public static final String ANCHOR_ID = "anchor_id";

    public static final String REPORT_ID = "report_id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
