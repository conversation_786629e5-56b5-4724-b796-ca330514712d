package com.quantchi.knowledge.center.bean.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.quantchi.knowledge.center.bean.vo.TocEntryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * AI报告详情展示类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@ApiModel(value = "AI报告详情展示类")
public class AiReportDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("报告ID")
    private Long id;

    @ApiModelProperty("报告标题")
    private String title;

    @ApiModelProperty("报告描述")
    private String description;

    @ApiModelProperty("报告主题")
    private String theme;

    @ApiModelProperty("参考模板文件ID")
    private Long templateFileId;

    @ApiModelProperty("参考模板文件名")
    private String templateFileName;

    @ApiModelProperty("生成的大纲内容(TocEntryVO列表)")
    private List<TocEntryVO> outline;

    @ApiModelProperty("生成的报告内容")
    private String preserveData;

    @ApiModelProperty("大纲生成状态：0-未生成，1-已生成，2-生成失败")
    private Integer outlineStatus;

    @ApiModelProperty("报告生成状态：0-未生成，1-生成中，2-已生成，3-生成失败")
    private Integer contentStatus;

    @ApiModelProperty("展示状态：0-未公开，1-已公开，2-已下架")
    private Integer displayStatus;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
