package com.quantchi.knowledge.center.bean.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业曾用名信息
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
@ApiModel(value = "企业曾用名信息")
public class CompanyUsedNameVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("曾用名")
    private String usedName;

    @ApiModelProperty("使用开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty("使用结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty("使用时间段描述")
    private String timePeriod;
}
