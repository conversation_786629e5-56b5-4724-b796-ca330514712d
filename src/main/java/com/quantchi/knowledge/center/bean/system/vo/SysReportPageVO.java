package com.quantchi.knowledge.center.bean.system.vo;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.quantchi.knowledge.center.bean.entity.SysReport;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@ApiModel(value = "报告列表展示类", description = "报告表")
@Slf4j
public class SysReportPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("报告名称")
    private String title;

    @ApiModelProperty("作者")
    private String author;

    @ApiModelProperty("发布机构")
    private String issuingAgency;

    @ApiModelProperty("报告介绍")
    private String introduce;

    @ApiModelProperty("摘要")
    private String summarize;

    @ApiModelProperty("产业标签列表")
    private List<String> industryTags;

    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date publishDate;

    @ApiModelProperty("文件id")
    private Long fileId;

    @ApiModelProperty("文件下载地址")
    private String downloadUrl;

    @ApiModelProperty("文件预览地址")
    private String previewUrl;

    @ApiModelProperty("文件首页图片")
    private String homePageImageUrl;

    @ApiModelProperty("查看量")
    private Integer clickTimes;

    @ApiModelProperty("下载量")
    private Integer downloadTimes;

    @ApiModelProperty("展示状态：0-未公开，1-已公开，2-已下架")
    private Integer displayStatus;

    @ApiModelProperty("审核状态：0-未审核，1-已审核，2-审核不通过")
    private Integer auditStatus;

    @ApiModelProperty("上次编辑于多少时间前")
    private String lastEditedAgo;

    public static SysReportPageVO build(final SysReport sysReport) {
        final SysReportPageVO sysReportPageVO = new SysReportPageVO();
        sysReportPageVO.setId(sysReport.getId());
        sysReportPageVO.setTitle(sysReport.getTitle());
        sysReportPageVO.setAuthor(sysReport.getAuthor());
        sysReportPageVO.setIssuingAgency(sysReport.getIssuingAgency());
        sysReportPageVO.setIntroduce(sysReport.getIntroduce());
        sysReportPageVO.setSummarize(sysReport.getSummarize());
        sysReportPageVO.setPublishDate(sysReport.getPublishDate());
        // 拼接用户token，用于记录下载行为
        sysReportPageVO.setDownloadUrl(sysReport.getDownloadUrl() + "&token=" + StpUtil.getTokenValue());
        sysReportPageVO.setPreviewUrl(sysReport.getPreviewUrl());
        final String homePageImageUrl = sysReport.getHomePageImageUrl();
        if (CharSequenceUtil.isNotBlank(homePageImageUrl)) {
            // 判断预览图片是否可用
            try {
                final String s = HttpUtil.get(homePageImageUrl, 1000);
                if (s == null || s.contains("Error Page")) {
                    sysReportPageVO.setHomePageImageUrl(null);
                } else {
                    sysReportPageVO.setHomePageImageUrl(homePageImageUrl);
                }
            } catch (Exception e) {
                log.error("报告首页图片获取失败", e);
            }
        } else {
            sysReportPageVO.setHomePageImageUrl(null);
        }
        sysReportPageVO.setClickTimes(sysReport.getClickTimes());
        sysReportPageVO.setDownloadTimes(sysReport.getDownloadTimes());
        sysReportPageVO.setDisplayStatus(sysReport.getDisplayStatus());
        sysReportPageVO.setAuditStatus(sysReport.getAuditStatus());
        sysReportPageVO.setLastEditedAgo(calculateTimeAgo(sysReport.getUpdateTime()));
        return sysReportPageVO;
    }

    /**
     * 构建报告页面VO对象（包含产业标签）
     *
     * @param sysReport 报告实体
     * @param industryTags 产业标签列表
     * @return 报告页面VO对象
     */
    public static SysReportPageVO build(final SysReport sysReport, final List<String> industryTags) {
        final SysReportPageVO sysReportPageVO = build(sysReport);
        sysReportPageVO.setIndustryTags(industryTags);
        return sysReportPageVO;
    }

    /**
     * 计算时间差，返回友好的时间描述
     * 60s 内展示秒，60 分钟内展示分钟，24 小时内展示小时，超过 24 小时展示天
     *
     * @param updateTime 更新时间
     * @return 时间差描述
     */
    private static String calculateTimeAgo(final Date updateTime) {
        if (updateTime == null) {
            return "未知";
        }

        final long currentTime = System.currentTimeMillis();
        final long updateTimeMillis = updateTime.getTime();
        final long diffMillis = currentTime - updateTimeMillis;

        // 如果时间差为负数（未来时间），返回"刚刚"
        if (diffMillis < 0) {
            return "刚刚";
        }

        final long diffSeconds = diffMillis / 1000;
        final long diffMinutes = diffSeconds / 60;
        final long diffHours = diffMinutes / 60;
        final long diffDays = diffHours / 24;

        if (diffSeconds < 60) {
            // 60秒内展示秒
            return diffSeconds + "秒前";
        } else if (diffMinutes < 60) {
            // 60分钟内展示分钟
            return diffMinutes + "分钟前";
        } else if (diffHours < 24) {
            // 24小时内展示小时
            return diffHours + "小时前";
        } else {
            // 超过24小时展示天
            return diffDays + "天前";
        }
    }

}
