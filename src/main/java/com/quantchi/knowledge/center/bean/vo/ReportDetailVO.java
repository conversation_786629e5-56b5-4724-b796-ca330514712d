package com.quantchi.knowledge.center.bean.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 报告详情VO
 *
 * <AUTHOR>
 * @date 2025/03/20
 */
@Data
@ApiModel("报告详情VO")
public class ReportDetailVO {

    @ApiModelProperty("报告ID")
    private Long id;

    @ApiModelProperty("报告标题")
    private String title;

    @ApiModelProperty("关键词列表")
    private List<String> keywords;

    @ApiModelProperty("产业链ID")
    private String chainId;

    @ApiModelProperty("报告类型")
    private String type;

    @ApiModelProperty("报告内容数据")
    private String preserveData;

    @ApiModelProperty("目录列表")
    private List<IndustryChainReportVO.TocEntryVO> tocEntries;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
