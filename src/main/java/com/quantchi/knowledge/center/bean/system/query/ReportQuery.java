package com.quantchi.knowledge.center.bean.system.query;

import com.quantchi.knowledge.ai.bo.PageBO;
import com.quantchi.knowledge.center.bean.model.IdNameModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@ApiModel("报告列表查询")
public class ReportQuery extends PageBO {

    @ApiModelProperty(value = "关键词")
    private String keyword;

    @ApiModelProperty(value = "报告类型")
    private List<IdNameModel> tagList;

}
