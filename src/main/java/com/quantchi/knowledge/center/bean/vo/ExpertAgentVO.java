package com.quantchi.knowledge.center.bean.vo;

import com.quantchi.knowledge.center.bean.entity.SysAgentConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 专家智能体VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@ApiModel(value = "ExpertAgentVO", description = "专家智能体VO")
public class ExpertAgentVO {

    @ApiModelProperty("智能体框架类型")
    private String framework;

    @ApiModelProperty("智能体名称")
    private String name;

    @ApiModelProperty("排序索引")
    private Integer index;

    @ApiModelProperty("智能体代号")
    private String key;

    @ApiModelProperty("智能体描述")
    private String desc;

    public static ExpertAgentVO toVO(SysAgentConfig sysAgentConfig) {
        final ExpertAgentVO expertAgentVO = new ExpertAgentVO();
        expertAgentVO.setFramework(sysAgentConfig.getFramework());
        expertAgentVO.setName(sysAgentConfig.getName());
        expertAgentVO.setIndex(sysAgentConfig.getSort());
        expertAgentVO.setKey(sysAgentConfig.getAgentKey());
        expertAgentVO.setDesc(sysAgentConfig.getDescription());
        return expertAgentVO;
    }
}
