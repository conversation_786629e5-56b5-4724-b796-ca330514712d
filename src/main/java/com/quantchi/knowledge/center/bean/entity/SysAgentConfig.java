package com.quantchi.knowledge.center.bean.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.knowledge.center.bean.system.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 智能体配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "AgentConfig对象", description = "智能体配置表")
@TableName("sys_agent_config")
public class SysAgentConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId
    private Long id;

    @ApiModelProperty("智能体名称")
    private String name;

    @ApiModelProperty("智能体代号")
    private String agentKey;

    @ApiModelProperty("智能体描述")
    private String description;

    @ApiModelProperty("智能体API密钥")
    private String apiKey;

    @ApiModelProperty("智能体域名地址")
    private String domainUrl;

    @ApiModelProperty("智能体接口地址")
    private String apiPath;

    @ApiModelProperty("智能体框架类型")
    private String framework;

    @ApiModelProperty("排序索引")
    private Integer sort;

    @ApiModelProperty("是否启用")
    private Boolean enabled;

    public static final String ID = "id";
    public static final String NAME = "name";
    public static final String KEY = "key";
    public static final String DESCRIPTION = "description";
    public static final String API_KEY = "api_key";
    public static final String DOMAIN_URL = "domain_url";
    public static final String API_PATH = "api_path";
    public static final String FRAMEWORK = "framework";
    public static final String INDEX = "index";
    public static final String CREATED_AT = "created_at";
    public static final String MESSAGES_JSON = "messages_json";
    public static final String ENABLED = "enabled";
}
