package com.quantchi.knowledge.center.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.knowledge.ai.bo.GeoQueryBO;
import com.quantchi.knowledge.center.bean.bo.IndustryAnalysisCompanyListBO;
import com.quantchi.knowledge.center.bean.constant.Constants;
import com.quantchi.knowledge.center.bean.entity.*;
import com.quantchi.knowledge.center.bean.enums.CustomTypeEnum;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.export.OverseasCompanyListExport;
import com.quantchi.knowledge.center.bean.model.*;
import com.quantchi.knowledge.center.bean.vo.NameCountVO;
import com.quantchi.knowledge.center.common.WildCardQueryStrategyEnum;
import com.quantchi.knowledge.center.config.properties.KeywordSearchProperties;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.*;
import com.quantchi.knowledge.center.service.index.PatentService;
import com.quantchi.knowledge.center.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static com.quantchi.knowledge.center.bean.constant.Constants.*;
import static com.quantchi.knowledge.center.service.impl.TechnologyPortraitServiceImpl.determineIpcField;
import static com.quantchi.knowledge.center.util.ElasticsearchBuilder.getFieldTypeByMapping;
import static com.quantchi.knowledge.center.util.LocalFileUtil.getActualFilePath;

/**
 * <AUTHOR>
 * @date 2022/7/21 14:03
 */
@Service
@Slf4j
public class LibraryInfoService extends CommonUseService {

    protected final static Map<String, LibraryInfoService> libraryInfoServiceMap = new HashMap<>();

    private final NavigationSettings navigationSettings;

    private final IDmDivisionService dmDivisionService;

    @Autowired
    private SchemaNecService schemaNecService;

    @Autowired
    private SchemaSecService schemaSecService;

    @Autowired
    private SchemaFecService schemaFecService;

    @Autowired
    @Lazy
    private ICompanyService companyService;

    @Autowired
    private ISchemaGtcService schemaGtcService;

    @Autowired
    private ISchemaDtcService schemaDtcService;

    @Autowired
    private PatentService patentService;

    @Autowired
    private ICompanyTagSchemaService companyTagSchemaService;

    @Autowired
    private INodeService nodeService;

    @Autowired
    private IIndustryChainService industryChainService;

    public LibraryInfoService(final ElasticsearchHelper elasticsearchHelper, final NavigationSettings navigationSettings, final KeywordSearchProperties keywordSearchProperties, final IDmDivisionService dmDivisionService) {
        super(elasticsearchHelper, navigationSettings, keywordSearchProperties);
        this.navigationSettings = navigationSettings;
        this.dmDivisionService = dmDivisionService;
    }

    public static List<Map<String, Object>> sortByCompanyIds(
            final List<Map<String, Object>> list,
            final List<String> companyIds,
            boolean isDesc,
            Integer pageNum,
            Integer pageSize) {
        // 参数验证
        if (list == null || companyIds == null) {
            return list;
        }

        try {
            // 创建顺序映射
            Map<String, Integer> orderMap = new HashMap<>(companyIds.size());
            for (int i = 0; i < companyIds.size(); i++) {
                orderMap.put(companyIds.get(i), i);
            }

            // 排序
            final List<Map<String, Object>> sortedList = list.stream()
                    .filter(map -> map != null && map.get("id") != null)
                    .sorted((a, b) -> {
                        String idA = (String) a.get("id");
                        String idB = (String) b.get("id");

                        Integer orderA = orderMap.get(idA);
                        Integer orderB = orderMap.get(idB);

                        // 处理不在orderMap中的id
                        if (orderA == null && orderB == null) {
                            return 0;
                        }
                        if (orderA == null) {
                            return 1;
                        }
                        if (orderB == null) {
                            return -1;
                        }
                        // 根据排序方向返回比较结果
                        return isDesc ? orderB.compareTo(orderA) : orderA.compareTo(orderB);
                    })
                    .collect(Collectors.toList());
            return getPageResult(sortedList, pageNum, pageSize);
        } catch (Exception e) {
            log.error("排序失败", e);
            return list;
        }
    }

    /**
     * 处理分页
     */
    private static List<Map<String, Object>> getPageResult(
            List<Map<String, Object>> list,
            Integer pageNum,
            Integer pageSize) {
        // 默认值处理
        pageNum = pageNum == null || pageNum < 1 ? 1 : pageNum;
        pageSize = pageSize == null || pageSize < 1 ? 10 : pageSize;

        // 计算总数和分页
        int total = list.size();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        // 处理越界情况
        if (startIndex >= total) {
            return Collections.emptyList();
        }

        // 截取分页数据
        return list.subList(startIndex, endIndex);
    }

    public static Boolean parseSortString(String sortStr, final String field) {
        if (StringUtils.isEmpty(sortStr)) {
            return false;
        }
        // 分割多个排序条件
        final Map<String, String> map = Arrays.stream(sortStr.split(";"))
                .filter(StringUtils::isNotEmpty)
                .map(item -> item.split(":"))
                .filter(parts -> parts.length == 2)
                .collect(Collectors.toMap(
                        parts -> parts[0],
                        parts -> parts[1],
                        (v1, v2) -> v1  // 处理重复key
                ));
        final String aDefault = map.getOrDefault(field, "desc");
        return !aDefault.contains("desc");
    }

    public static String removeSortField(String sortStr, String fieldToRemove) {
        if (StringUtils.isEmpty(sortStr) || StringUtils.isEmpty(fieldToRemove)) {
            return sortStr;
        }

        return Arrays.stream(sortStr.split(";"))
                .filter(StringUtils::isNotEmpty)
                .filter(item -> !item.startsWith(fieldToRemove + ":"))
                .collect(Collectors.joining(";"));
    }

    // 使用示例
    public static void main(String[] args) {
        String sortStr = "date:desc";

        // 移除字段
        String newSortStr = removeSortField(sortStr, "patentOwnershipCount");  // 得到 "date:desc"

        System.out.println("Patent direction: " + parseSortString(sortStr, "patentOwnershipCount"));
        System.out.println("New sort string: " + newSortStr);
    }

    @PostConstruct
    public void init() {
        libraryInfoServiceMap.put("default", this);
    }

    public LibraryInfoService getService(final String index) {
        final LibraryInfoService libraryInfoService = libraryInfoServiceMap.get(index);
        if (libraryInfoService != null) {
            return libraryInfoService;
        } else {
            return libraryInfoServiceMap.get("default");
        }
    }

    public void dealWthCompanyId(final Object patentees) {
        if (patentees instanceof List) {
            final List<String> companyIdList = new ArrayList<>();
            final List<Map<String, Object>> patenteesList = (List<Map<String, Object>>) patentees;
            for (final Map<String, Object> patenteesMap : patenteesList) {
                final Object companyId = patenteesMap.get("id");
                if (companyId instanceof String) {
                    companyIdList.add((String) companyId);
                }
            }
            if (CollUtil.isNotEmpty(companyIdList)) {
                final List<Company> companies = companyService.listByIds(companyIdList);
                final Set<String> existCompanyIdList = companies.stream().map(Company::getId).collect(Collectors.toSet());
                for (final Map<String, Object> patenteesMap : patenteesList) {
                    final Object companyId = patenteesMap.get("id");
                    if (companyId instanceof String) {
                        if (!existCompanyIdList.contains((String) companyId)) {
                            patenteesMap.put("id", null);
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据业务需求定制返回参数
     */
    @Override
    public EsPageResult specialDealWithLibraryPageResult(final EsPageResult pageResult) {
        final List<Map<String, Object>> list = pageResult.getList();
        if (CollUtil.isEmpty(list)) {
            return pageResult;
        }
        for (final Map<String, Object> sourceAsMap : list) {
            final Object labels = sourceAsMap.get("labels");
            if (labels instanceof String) {
                final String[] split = ((String) labels).split(";");
                sourceAsMap.put("labels", split);
            }
        }
        return pageResult;
    }

    /**
     * 构建特殊业务处理的boolQuery
     *
     * @param boolQuery
     */
    @Override
    protected void specialBuildBoolQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries, final GeoQueryBO geoQuery) {

    }

    /**
     * 针对某些库的特殊逻辑处理，返回整理后的排序
     */
    @Override
    public String specialSortForLibrarySearch(final BoolQueryBuilder boolQuery, final String sort, final String keyword) {
        return sort;
    }

    @Override
    protected void specialSearchSourceForLibrarySearch(final String esIndex, final SearchSourceBuilder searchSource,
                                                       final Map<String, List<String>> termQueries, final String keyword, final BoolQueryBuilder boolQuery) {
    }

    /**
     * 获取筛选项
     */
    public List<CustomIndexNavSetting> getNavSetting(final String index, final String termQuery) {
        final List<CustomIndexNavSetting> customIndexNavSettings = navigationSettings.getIndexNavSettingMap().get(index);
        if (CollUtil.isEmpty(customIndexNavSettings)) {
            return Collections.emptyList();
        }
        // 特别处理构建产业领域
        customIndexNavSettings.forEach(customIndexNavSetting -> {
            buildIndustryNavSetting(customIndexNavSetting);
            buildNecNavSetting(customIndexNavSetting);
            buildSecNavSetting(customIndexNavSetting);
            buildFecNavSetting(customIndexNavSetting);
            buildGtcNavSetting(customIndexNavSetting);
            buildDtcNavSetting(customIndexNavSetting);
            buildCapitalNavSetting(customIndexNavSetting);
            buildRankNavSetting(customIndexNavSetting);
            buildChainNodeNavSetting(customIndexNavSetting);
            buildChainNavSetting(customIndexNavSetting);

        });
        // 2.级联参数根据父级的scope参数构建children
        customIndexNavSettings.forEach(customIndex -> {
            final String[] type = {""};
            final List<CustomIndexNavSetting> children = customIndex.getChildren();
            if (children != null) {
                children.forEach(ch -> {
                    type[0] = ch.getType();
                    final List<CustomIndexNavSetting> settings = new LinkedList<>();
                    final List<String> scope = ch.getScope();
                    if (scope == null) {
                        return;
                    }
                    // 通过scope构建子级的list
                    scope.forEach(sc -> {
                        final CustomIndexNavSetting customIndexNavSetting = new CustomIndexNavSetting();
                        // 使用名称作为field
                        customIndexNavSetting.setField(sc);
                        customIndexNavSetting.setFieldName(sc);
                        settings.add(customIndexNavSetting);
                    });
                    ch.setChildren(settings);
                    ch.setField(ch.getFieldName());
                    ch.setScope(null);
                });
            }
            customIndex.setType(type[0]);
        });
        return customIndexNavSettings;
    }

    /**
     * 构建产业领域的导航栏参数
     *
     * @return
     */
    public void buildIndustryNavSetting(final CustomIndexNavSetting setting) {
        if (!Objects.equals(setting.getField(), "chain_node.id")) {
            return;
        }
        setting.setField("chain_node.id");
        setting.setFieldName("国家新兴产业");
        setting.setCustomType(CustomTypeEnum.CASCADE.getType());
        setting.setSearchType(4);
        final List<CustomIndexNavSetting> chainChildrenList = new LinkedList<>();
        chainChildrenList.add(new CustomIndexNavSetting("国民经济行业"));
        chainChildrenList.add(new CustomIndexNavSetting("战新产业"));
        chainChildrenList.add(new CustomIndexNavSetting("未来产业"));
        chainChildrenList.add(new CustomIndexNavSetting("量知产业"));
        setting.setChildren(chainChildrenList);
    }

    public void buildNecNavSetting(final CustomIndexNavSetting setting) {
        if (!Objects.equals(setting.getField(), "nec")) {
            return;
        }
        setting.setField("nec");
        setting.setFieldName("国民经济行业");
        setting.setCustomType(CustomTypeEnum.CASCADE.getType());
        setting.setSearchType(4);
        final List<Tree<String>> tree = schemaNecService.getTree(null);
        if (CollUtil.isEmpty(tree)) {
            return;
        }
        setting.setChildren(CustomIndexNavSetting.convertTreeList(tree.get(0).getChildren()));
    }

    public void buildSecNavSetting(final CustomIndexNavSetting setting) {
        if (!Objects.equals(setting.getField(), "sec")) {
            return;
        }
        setting.setField("sec");
        setting.setFieldName("战新产业");
        setting.setCustomType(CustomTypeEnum.CASCADE.getType());
        setting.setSearchType(4);
        final List<Tree<String>> tree = schemaSecService.getTree(null);
        if (CollUtil.isEmpty(tree)) {
            return;
        }
        setting.setChildren(CustomIndexNavSetting.convertTreeList(tree.get(0).getChildren()));
    }

    public void buildFecNavSetting(final CustomIndexNavSetting setting) {
        if (!Objects.equals(setting.getField(), "fec")) {
            return;
        }
        setting.setField("fec");
        setting.setFieldName("未来产业");
        setting.setCustomType(CustomTypeEnum.CASCADE.getType());
        setting.setSearchType(4);
        final List<Tree<String>> tree = schemaFecService.getTree(null);
        if (CollUtil.isEmpty(tree)) {
            return;
        }
        setting.setChildren(CustomIndexNavSetting.convertTreeList(tree.get(0).getChildren()));
    }

    public void buildGtcNavSetting(final CustomIndexNavSetting setting) {
        if (!Objects.equals(setting.getField(), "gtc")) {
            return;
        }
        setting.setField("gtc");
        setting.setFieldName("绿色技术");
        setting.setCustomType(CustomTypeEnum.CASCADE.getType());
        setting.setSearchType(4);
        final List<Tree<String>> tree = schemaGtcService.getTree(null);
        if (CollUtil.isEmpty(tree)) {
            return;
        }
        setting.setChildren(CustomIndexNavSetting.convertTreeList(tree.get(0).getChildren()));
    }

    public void buildDtcNavSetting(final CustomIndexNavSetting setting) {
        if (!Objects.equals(setting.getField(), "dtc")) {
            return;
        }
        setting.setField("dtc");
        setting.setFieldName("数字技术");
        setting.setCustomType(CustomTypeEnum.CASCADE.getType());
        setting.setSearchType(4);
        final List<Tree<String>> tree = schemaDtcService.getTree(null);
        if (CollUtil.isEmpty(tree)) {
            return;
        }
        setting.setChildren(CustomIndexNavSetting.convertTreeList(tree.get(0).getChildren()));
    }

    private void buildChainNavSetting(CustomIndexNavSetting setting) {
        if (!Objects.equals(setting.getField(), "chain")) {
            return;
        }
        setting.setField("chain");
        setting.setFieldName("量知产业链");
        setting.setCustomType(CustomTypeEnum.CASCADE.getType());
        setting.setSearchType(4);

        // 获取产业链分类
        final List<Knowledge> list = industryChainService.list(Wrappers.<Knowledge>query()
                .select("distinct label")
                .eq("is_valid", 1));
        setting.setScope(list.stream().map(Knowledge::getLabel).collect(Collectors.toList()));
    }

    private void buildChainNodeNavSetting(CustomIndexNavSetting setting) {
        if (!Objects.equals(setting.getField(), "chain_node")) {
            return;
        }
        setting.setField("chain_node");
        setting.setFieldName("量知产业链");
        setting.setCustomType(CustomTypeEnum.CASCADE.getType());
        setting.setSearchType(4);
        // 获取产业链分类
        final List<Knowledge> list = industryChainService.list(Wrappers.<Knowledge>query()
                .select("distinct label")
                .eq("is_valid", 1));
        setting.setScope(list.stream().map(Knowledge::getLabel).collect(Collectors.toList()));
    }

    private void buildCapitalNavSetting(CustomIndexNavSetting setting) {
        if (!Objects.equals(setting.getField(), "capital")) {
            return;
        }
        setting.setField("capital");
        setting.setFieldName("资本类型");
        setting.setCustomType(CustomTypeEnum.CASCADE.getType());
        setting.setSearchType(4);

        final List<Tree<String>> tree = companyTagSchemaService.getTree("资本类型标签");
        if (CollUtil.isEmpty(tree)) {
            return;
        }
        setting.setChildren(CustomIndexNavSetting.convertTreeList(tree.get(0).getChildren()));
    }

    private void buildRankNavSetting(CustomIndexNavSetting setting) {
        if (!Objects.equals(setting.getField(), "rank")) {
            return;
        }
        setting.setField("rank");
        setting.setFieldName("榜单标签");
        setting.setCustomType(CustomTypeEnum.CASCADE.getType());
        setting.setSearchType(4);

        final List<Tree<String>> tree = companyTagSchemaService.getTree("榜单标签");
        if (CollUtil.isEmpty(tree)) {
            return;
        }
        setting.setChildren(CustomIndexNavSetting.convertTreeList(tree.get(0).getChildren()));
    }

    /**
     * 根据字段的自定义类型和筛选类型进行导航栏参数查询语句的构建
     */
    @Override
    public void buildBoolQueryForNavigationField(final String index, final String field,
                                                 final List<String> valueList,
                                                 final CustomIndexNavSetting indexNavigationSetting,
                                                 final BoolQueryBuilder boolQuery,
                                                 final Operator operator) {
        if (StrUtil.isBlank(field) || CollUtil.isEmpty(valueList)) {
            return;
        }
        final Integer customType = indexNavigationSetting.getCustomType();
        final Integer searchType = indexNavigationSetting.getSearchType();
        final List<String> matchTypes = indexNavigationSetting.getMatchTypes();
        final BasicBoolQuery queryBO = new BasicBoolQuery();
        queryBO.setBoolQueryBuilder(boolQuery);
        queryBO.setField(field);
        queryBO.setValues(valueList);
        queryBO.setMatchTypes(matchTypes);
        queryBO.setFieldKeyword(indexNavigationSetting.getFieldKeyword());
        queryBO.setSearchType(searchType);
        queryBO.setOperator(operator);
        queryBO.setIndex(index);
        queryBO.setOperator(Operator.AND);
        // 2.级联搜索的字段需要额外判断搜索的字段名
        if (Objects.equals(customType, CustomTypeEnum.CASCADE.getType())) {
            if ("awards".equals(field) || "honors".equals(field)) {
                // 荣誉或奖项级联搜索
                final List<String> levelIntersection = (List<String>) CollUtil.intersection(valueList, LEVEL_LIST);
                if (CollUtil.isNotEmpty(levelIntersection)) {
                    queryBO.setField(field + ".level");
                    queryBO.setValues(levelIntersection);
                    queryBO.setOperator(Operator.OR);
                    ElasticsearchBuilder.buildBoolQuery(queryBO);
                }
                queryBO.setField(field + ".name");
                queryBO.setValues((List<String>) CollUtil.subtract(valueList, LEVEL_LIST));
                ElasticsearchBuilder.buildBoolQuery(queryBO);
                return;
            }
        }
        // 地区自定义字段
        if (Objects.equals(field, "region")) {
            // 获取地区id对应的省份名或城市名
            final List<DmDivision> dmDivisions = dmDivisionService.listByIds(valueList);
            final List<String> provinceNameList = dmDivisions.stream()
                    .filter(dmDivision -> dmDivision.getLevel().equals(PROVINCE_LEVEL))
                    .map(DmDivision::getName).collect(Collectors.toList());
            final List<String> cityNameList = dmDivisions.stream()
                    .filter(dmDivision -> dmDivision.getLevel().equals(CITY_LEVEL))
                    .map(DmDivision::getName).collect(Collectors.toList());
            final List<String> areaNameList = dmDivisions.stream()
                    .filter(dmDivision -> dmDivision.getLevel().equals(AREA_LEVEL))
                    .map(DmDivision::getName).collect(Collectors.toList());
            final List<String> provinceCodeList = dmDivisions.stream()
                    .filter(dmDivision -> dmDivision.getLevel().equals(PROVINCE_LEVEL))
                    .map(DmDivision::getCode).collect(Collectors.toList());
            final List<String> cityCodeList = dmDivisions.stream()
                    .filter(dmDivision -> dmDivision.getLevel().equals(CITY_LEVEL))
                    .map(DmDivision::getCode).collect(Collectors.toList());
            final List<String> areaCodeList = dmDivisions.stream()
                    .filter(dmDivision -> dmDivision.getLevel().equals(AREA_LEVEL))
                    .map(DmDivision::getCode).collect(Collectors.toList());
            final Map<String, List<String>> fieldValueMap = new HashMap<>(2);
            if (CollUtil.isNotEmpty(provinceNameList)) {
                fieldValueMap.put(PROVINCE, provinceNameList);
            }
            if (CollUtil.isNotEmpty(cityNameList)) {
                fieldValueMap.put(CITY, cityNameList);
            }
            if (CollUtil.isNotEmpty(areaNameList)) {
                fieldValueMap.put(AREA, areaNameList);
            }
            if (CollUtil.isNotEmpty(provinceCodeList)) {
                fieldValueMap.put(PROVINCE_CODE, provinceCodeList);
            }
            if (CollUtil.isNotEmpty(cityCodeList)) {
                fieldValueMap.put(CITY_CODE, cityCodeList);
            }
            if (CollUtil.isNotEmpty(areaCodeList)) {
                fieldValueMap.put(AREA_CODE, areaCodeList);
            }
            ElasticsearchBuilder.buildMultiBoolQuery(boolQuery, fieldValueMap);
            return;
        }
        // 3.将前端传入的value转换为实际查询的value
        final List<String> rangeFields = navigationSettings
                .getRangeFields(indexNavigationSetting, valueList, searchType);
        if (!CollUtil.isEmpty(rangeFields)) {
            queryBO.setValues(rangeFields);
        }
        // 4.构建查询
        ElasticsearchBuilder.buildBoolQuery(queryBO);
    }

    /**
     * 高级搜索
     *
     * @param searchQuery
     * @return
     */
    public EsPageResult newAdvancedSearch(final PatentAdvancedSearchQuery searchQuery) {
        final String type = searchQuery.getType();
        final String esIndex = EsIndexEnum.getEsIndexByType(type);
        final Integer pageNum = searchQuery.getPageNum();
        final Integer pageSize = searchQuery.getPageSize();
        final List<SearchCondition> conditionList = searchQuery.getConditionList();
        final String keyword = searchQuery.getKeyword();
        String sort = searchQuery.getSort();
        if (StringUtils.isEmpty(esIndex)) {
            throw new BusinessException("缺少必要的查询条件");
        }
        conditionList.removeIf(condition -> {
            final Integer customType = condition.getCustomType();
            final List<String> valueList = condition.getValueList();
            final Date beginDate = condition.getBeginDate();
            final Date endDate = condition.getEndDate();
            final String field = condition.getField();
            if (StrUtil.isBlank(field)) {
                return true;
            }
            if (customType == null) {
                return true;
            }
            // 非日期类型需要传值
            if (customType != 1 && CollUtil.isEmpty(valueList)) {
                return true;
            }
            return customType == 1 && beginDate == null && endDate == null;
        });
        if (CollUtil.isEmpty(conditionList)) {
            final MultidimensionalQuery query = new MultidimensionalQuery();
            query.setSort(sort);
            query.setIndex(esIndex);
            query.setPageNum(pageNum);
            query.setPageSize(pageSize);
            query.setKeyword(keyword);
            query.setIsForExport(searchQuery.getIsExport());
            return patentService.getLibraryList(query);
        }
        //根据检索条件进行搜索
        BoolQueryBuilder complexQuery = QueryBuilders.boolQuery();
        patentService.specialBuildBoolQuery(complexQuery, null, null);
        // 关键词搜索
        final List<KeywordSearchProperty> keywordSearchList = keywordSearchProperties.getKeywordSearchList(esIndex);
        final String actualKeyword;
        if (StrUtil.isNotBlank(keyword)) {
            final HighlightBuilder highlightBuilder = new HighlightBuilder();
            actualKeyword = buildKeywordBoolQuery(esIndex, keyword, complexQuery, highlightBuilder, keywordSearchList, searchQuery.getIsNeedCorrect());
        } else {
            actualKeyword = null;
        }
        for (int i = 0; i < conditionList.size(); i++) {
            final SearchCondition currentCondition = conditionList.get(i);
            // 判断连接符
            if (i == 0) {
                // 根据条件判断使用should还是must
                if (conditionList.size() > 1) {
                    final SearchCondition secondCondition = conditionList.get(1);
                    if (Operator.OR.toString().equals(secondCondition.getOperator())) {
                        complexQuery = complexQuery.should(buildNewAdvancedQuery(esIndex, currentCondition));
                    } else {
                        complexQuery = complexQuery.must(buildNewAdvancedQuery(esIndex, currentCondition));
                    }
                } else {
                    complexQuery = complexQuery.must(buildNewAdvancedQuery(esIndex, currentCondition));
                }
            } else {
                if (null != currentCondition.getOperator() && Operator.OR.toString().equals(currentCondition.getOperator())) {
                    complexQuery = complexQuery.should(buildNewAdvancedQuery(esIndex, currentCondition));
                } else {
                    complexQuery = complexQuery.must(buildNewAdvancedQuery(esIndex, currentCondition));
                }
            }
        }
        final EsIndexEnum indexEnum = EsIndexEnum.getIndexEnumByEsIndex(esIndex);
        final String sortField;
        if (indexEnum != null && CharSequenceUtil.isBlank(sort)) {
            sortField = indexEnum.getSort();
            if (StringUtils.isNotBlank(sortField)) {
                sort = sortField + ":" + "desc";
            }
        }
        final SearchSourceQuery sourceQuery = new SearchSourceQuery();
        sourceQuery.setQueryBuilder(complexQuery);
        sourceQuery.setSort(sort);
        sourceQuery.setPageNum(pageNum);
        sourceQuery.setPageSize(pageSize);
        sourceQuery.setKeyword(keyword);

        final SearchSourceBuilder searchSource = ElasticsearchBuilder.buildSearchSource(sourceQuery);
        // 获取结果
        final SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSource, esIndex, keyword);
        EsPageResult esPageResult = elasticsearchHelper.buildPageResultWithHighlight(searchResponse, keywordSearchList, keyword, true, esIndex);
        esPageResult = patentService.specialDealWithLibraryPageResult(esPageResult);
        // 5.判断是否需要纠错
        if (CharSequenceUtil.isNotBlank(actualKeyword)) {
            final ErrorCorrectionPageResult errorCorrectionPageResult = new ErrorCorrectionPageResult(esPageResult);
            errorCorrectionPageResult.setOriginalKeyword(keyword);
            errorCorrectionPageResult.setActualKeyword(actualKeyword);
            return errorCorrectionPageResult;
        }
//        storeHistory(keyword, pageNum, EsIndexEnum.PATENT.getEsIndex(), esPageResult.getList());
        return esPageResult;
    }

    /**
     * 新高级搜索条件参数构建
     *
     * @param index
     * @param searchCondition
     * @return
     */
    public BoolQueryBuilder buildNewAdvancedQuery(final String index, final SearchCondition searchCondition) {
        final String operator = searchCondition.getOperator();
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (operator.equals(Operator.OR.toString())) {
            boolQueryBuilder.should(buildBoolQueryForValue(index, searchCondition));
        } else {
            if (Objects.equals(operator, "NOT")) {
                boolQueryBuilder.mustNot(buildBoolQueryForValue(index, searchCondition));
            } else {
                boolQueryBuilder.must(buildBoolQueryForValue(index, searchCondition));
            }
        }
        return boolQueryBuilder;
    }

    private QueryBuilder buildBoolQueryForValue(final String index, final SearchCondition searchCondition) {
        final String field = searchCondition.getField();
        final Integer customType = searchCondition.getCustomType();
        final Date beginDate = searchCondition.getBeginDate();
        final Date endDate = searchCondition.getEndDate();
        final List<String> valueList = searchCondition.getValueList();
        // 日期类型
        if (customType == 1) {
            final RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(field);
            if (beginDate != null) {
                rangeQueryBuilder.gte(DateUtil.format(beginDate, NORM_DATE_PATTERN));
            }
            if (endDate != null) {
                rangeQueryBuilder.lte(DateUtil.format(endDate, NORM_DATE_PATTERN));
            }
            return rangeQueryBuilder;
        }
        // 产业链及节点查询
        if (Objects.equals(field, "chain_node")) {
            final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            final Map<String, List<String>> termQueries = new HashMap<String, List<String>>() {
                {
                    put("chain_node", valueList);
                }
            };
            generateChainNodeQuery(boolQuery, termQueries);
            return boolQuery;
        }
        // 产业领域
        if (Objects.equals(field, "chain_node.id")) {
            final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            final Map<String, List<String>> termQueries = new HashMap<String, List<String>>() {
                {
                    put("chain_node.id", valueList);
                }
            };
            generateChainQuery(boolQuery, termQueries);
            return boolQuery;
        }
        final List<CustomIndexNavSetting> indexNavigationSettings =
                navigationSettings.getIndexNavSettingMap().get(index);
        final List<CustomIndexNavSetting> navSettings =
                indexNavigationSettings.stream().filter(set -> set.getField().equals(field)).collect(Collectors.toList());
        CustomIndexNavSetting useSetting = null;
        if (CollUtil.isNotEmpty(navSettings)) {
            useSetting = navSettings.get(0);
        }
        if (customType == 0 || customType == 3) {
            final BoolQueryBuilder boolQuery = new BoolQueryBuilder();
            buildBoolQueryForNavigationField(index, field, valueList, useSetting, boolQuery, Operator.AND);
            return boolQuery;
        }
        final ActualFieldAndType keywordFieldTypeByMapping = getFieldTypeByMapping(index, field);
        final String type = keywordFieldTypeByMapping.getType();
        if (Objects.equals("text", type)) {
            // 如果是text类型，那么使用matchQuery，keyword类型使用wildcardQuery
            return QueryBuilders.matchPhraseQuery(field, valueList.get(0));
        } else {
            return QueryBuilders.wildcardQuery(field,
                    WildCardQueryStrategyEnum.getStrategyKeyword(valueList.get(0),
                            WildCardQueryStrategyEnum.ALL_LINK_ALL_QUERY.getType()));
        }
    }

    /**
     * 处理sec的id到name的映射
     *
     * @param list
     */
    public void dealWithSecName(final List<Map<String, Object>> list) {
        final List<String> secList = new ArrayList<>();
        for (final Map<String, Object> map : list) {
            final Object sec = map.get("sec");
            if (sec instanceof List) {
                secList.addAll((Collection<? extends String>) sec);
            }
        }
        if (CollUtil.isEmpty(secList)) {
            return;
        }
        final List<SchemaSec> schemaSecs = schemaSecService.list(Wrappers.<SchemaSec>lambdaQuery()
                .select(SchemaSec::getId, SchemaSec::getName)
                .in(SchemaSec::getId, secList));
        final Map<String, String> nameMap = schemaSecs.stream().collect(Collectors.toMap(SchemaSec::getId, SchemaSec::getName));
        for (final Map<String, Object> map : list) {
            final Object sec = map.get("sec");
            if (sec instanceof List) {
                final List<String> secList1 = (List<String>) sec;
                final List<String> nameList = secList1.stream().map(nameMap::get).collect(Collectors.toList());
                map.put("sec", nameList);
            }
        }
    }

    /**
     * 处理fec的id到name的映射
     *
     * @param list
     */
    public void dealWithFecName(final List<Map<String, Object>> list) {
        final List<String> fecList = new ArrayList<>();
        for (final Map<String, Object> map : list) {
            final Object fec = map.get("fec");
            if (fec instanceof List) {
                fecList.addAll((Collection<? extends String>) fec);
            }
        }
        if (CollUtil.isEmpty(fecList)) {
            return;
        }
        final List<SchemaFec> schemaFecs = schemaFecService.list(Wrappers.<SchemaFec>lambdaQuery()
                .select(SchemaFec::getId, SchemaFec::getName)
                .in(SchemaFec::getId, fecList));
        final Map<String, String> nameMap = schemaFecs.stream().collect(Collectors.toMap(SchemaFec::getId, SchemaFec::getName));
        for (final Map<String, Object> map : list) {
            final Object fec = map.get("fec");
            if (fec instanceof List) {
                final List<String> fecList1 = (List<String>) fec;
                final List<String> nameList = fecList1.stream().map(nameMap::get).collect(Collectors.toList());
                final Object sec = map.get("sec");
                if (sec instanceof List) {
                    nameList.addAll((Collection<? extends String>) sec);
                }
                map.put("sec", nameList);
            }
        }
    }

    /**
     * 处理nec的id到name的映射
     *
     * @param list
     */
    public void dealWithNecName(final List<Map<String, Object>> list) {
        final List<String> necList = new ArrayList<>();
        for (final Map<String, Object> map : list) {
            final Object nec = map.get("nec");
            if (nec instanceof List && ((List) nec).size() > 0) {
                final List<String> necDataList = (List<String>) nec;
                necList.add(necDataList.get(necDataList.size() - 1));
            }
        }
        if (CollUtil.isEmpty(necList)) {
            return;
        }
        final List<SchemaNec> schemaFecs = schemaNecService.list(Wrappers.<SchemaNec>lambdaQuery()
                .select(SchemaNec::getId, SchemaNec::getName)
                .in(SchemaNec::getId, necList));
        final Map<String, String> nameMap = schemaFecs.stream().collect(Collectors.toMap(SchemaNec::getId, SchemaNec::getName));
        for (final Map<String, Object> map : list) {
            final Object nec = map.get("nec");
            if (nec instanceof List && ((List) nec).size() > 0) {
                final List<String> necList1 = (List<String>) nec;
                final String necId = necList1.get(necList1.size() - 1);
                final List<String> nameList = new ArrayList<>();
                if (nameMap.containsKey(necId)) {
                    nameList.add(nameMap.get(necId));
                }
                final Object sec = map.get("sec");
                if (sec instanceof List) {
                    nameList.addAll((Collection<? extends String>) sec);
                }
                map.put("sec", nameList);
            }
        }
    }

    /**
     * 生成产业领域查询条件
     *
     * @param boolQuery
     * @param termQueries
     */
    public void generateChainQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries) {
        if (termQueries == null) {
            return;
        }
        if (!termQueries.containsKey("chain_node.id")) {
            return;
        }
        // 解析传入的列表，进行字符串分割解析，确定属于哪一种产业领域
        final List<String> chainNodeIds = termQueries.get("chain_node.id");
        termQueries.remove("chain_node.id");
        if (CollUtil.isEmpty(chainNodeIds)) {
            return;
        }
        int type = 0;
        final List<String> idList = new ArrayList<>(chainNodeIds.size());
        for (final String chainNodeId : chainNodeIds) {
            final String[] split = chainNodeId.split(":");
            if (split.length != 2) {
                return;
            }
            type = Integer.parseInt(split[0]);
            idList.add(split[1]);
        }
        // 获取所有子节点
        switch (type) {
            case 1:
                boolQuery.filter(QueryBuilders.termsQuery("nec",
                        TreeBuildUtils.getChildrenIdOfNodes(
                                schemaNecService.getTree(null), idList)));
                break;
            case 2:
                boolQuery.filter(QueryBuilders.termsQuery("sec",
                        TreeBuildUtils.getChildrenIdOfNodes(
                                schemaSecService.getTree(null), idList)));
                break;
            case 3:
                boolQuery.filter(QueryBuilders.termsQuery("fec",
                        TreeBuildUtils.getChildrenIdOfNodes(
                                schemaFecService.getTree(null), idList)));
                break;
            case 4:
                boolQuery.filter(QueryBuilders.termsQuery("icd", idList));
                break;
            default:
                break;
        }
    }

    /**
     * 生成产业链节点查询条件
     */
    public void generateChainsQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries) {
        if (termQueries == null) {
            return;
        }
        if (!termQueries.containsKey("chain")) {
            return;
        }
        // 解析传入的列表，进行字符串分割解析，确定属于哪一种产业领域
        final List<String> chainIds = termQueries.get("chain");
        termQueries.remove("chain");
        if (CollUtil.isEmpty(chainIds)) {
            return;
        }

        // 处理格式：去掉冒号前面的部分，只保留产业链值
        final List<String> processedChainIds = chainIds.stream()
                .map(chainId -> {
                    final String[] split = chainId.split(":");
                    // 如果包含冒号，返回冒号后面的部分；否则返回原值
                    return split.length > 1 ? "industry_" + split[1] : "industry_" + chainId;
                })
                .collect(Collectors.toList());

        // 根据 chainIds获取产业链名称
        final List<Knowledge> list = industryChainService.list(Wrappers.<Knowledge>lambdaQuery()
                        .select(Knowledge::getName)
                .in(Knowledge::getId, processedChainIds));

        boolQuery.filter(QueryBuilders.termsQuery("industry",
                list.stream().map(Knowledge::getName).collect(Collectors.toList())));
    }

    /**
     * 生成产业链节点查询条件
     */
    public void generateChainNodeQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries) {
        if (termQueries == null) {
            return;
        }
        if (!termQueries.containsKey("chain_node")) {
            return;
        }
        // 解析传入的列表，进行字符串分割解析，确定属于哪一种产业领域
        final List<String> chainNodeIds = termQueries.get("chain_node");
        termQueries.remove("chain_node");
        if (CollUtil.isEmpty(chainNodeIds)) {
            return;
        }

        Map<String, List<String>> chainNodeMap = new HashMap<>();
        for (final String chainNodeId : chainNodeIds) {
            final String[] split = chainNodeId.split(":");
            if (split.length < 2) {
                continue; // 跳过格式不正确的项
            }

            // 现在格式是 "分类:chainId:nodeId" 或 "分类:chainId"（根节点情况）
            final String chainId = split[1]; // chainId从split[1]获取

            if (chainNodeMap.containsKey(chainId)) {
                // 如果有nodeId（split.length >= 3），则添加到列表中
                if (split.length >= 3 && StrUtil.isNotBlank(split[2])) {
                    chainNodeMap.get(chainId).add(split[2]);
                }
                // 如果是根节点情况（split.length == 2），不添加nodeId，保持列表为空
            } else {
                List<String> nodeIds = new ArrayList<>();
                // 如果有nodeId（split.length >= 3），则添加到列表中
                if (split.length >= 3 && StrUtil.isNotBlank(split[2])) {
                    nodeIds.add(split[2]);
                }
                // 如果是根节点情况（split.length == 2），nodeIds保持为空列表
                chainNodeMap.put(chainId, nodeIds);
            }
        }

        // 创建一个新的 BoolQueryBuilder 来组合不同产业链的查询条件（OR关系）
        final BoolQueryBuilder chainNodeOrQuery = QueryBuilders.boolQuery();

        chainNodeMap.forEach((chainId, nodeIds) -> {
            // 获取所有子节点
            final List<String> nodeIdList = nodeService.getChildNodeIdList("industry_" + chainId, nodeIds);
            // 使用 should() 创建 OR 关系，而不是 filter() 的 AND 关系
            chainNodeOrQuery.should(ChainNodeQueryUtil.buildChainNodeQuery(chainId, nodeIdList));
        });

        // 如果有产业链查询条件，则添加到主查询中
        if (chainNodeOrQuery.should().size() > 0) {
            // 设置最小匹配数为1，确保至少匹配一个产业链条件
            chainNodeOrQuery.minimumShouldMatch(1);
            boolQuery.filter(chainNodeOrQuery);
        }
    }

    public void generateNecQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries) {
        final String field = "nec";
        if (termQueries == null) {
            return;
        }
        if (!termQueries.containsKey(field)) {
            return;
        }
        // 解析传入的列表，进行字符串分割解析，确定属于哪一种产业领域
        final List<String> chainNodeIds = termQueries.get(field);
        termQueries.remove(field);
        if (CollUtil.isEmpty(chainNodeIds)) {
            return;
        }
        boolQuery.filter(QueryBuilders.termsQuery(field,
                TreeBuildUtils.getChildrenIdOfNodes(
                        schemaNecService.getTree(null), chainNodeIds)));
    }

    public void generateSecQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries) {
        final String field = "sec";
        if (termQueries == null) {
            return;
        }
        if (!termQueries.containsKey(field)) {
            return;
        }
        // 解析传入的列表，进行字符串分割解析，确定属于哪一种产业领域
        final List<String> chainNodeIds = termQueries.get(field);
        termQueries.remove(field);
        if (CollUtil.isEmpty(chainNodeIds)) {
            return;
        }
        boolQuery.filter(QueryBuilders.termsQuery(field,
                TreeBuildUtils.getChildrenIdOfNodes(
                        schemaSecService.getTree(null), chainNodeIds)));
    }

    public void generateFecQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries) {
        final String field = "fec";
        if (termQueries == null) {
            return;
        }
        if (!termQueries.containsKey(field)) {
            return;
        }
        // 解析传入的列表，进行字符串分割解析，确定属于哪一种产业领域
        final List<String> chainNodeIds = termQueries.get(field);
        termQueries.remove(field);
        if (CollUtil.isEmpty(chainNodeIds)) {
            return;
        }
        boolQuery.filter(QueryBuilders.termsQuery(field,
                TreeBuildUtils.getChildrenIdOfNodes(
                        schemaFecService.getTree(null), chainNodeIds)));
    }

    public void generateGtcQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries) {
        final String field = "gtc";
        if (termQueries == null) {
            return;
        }
        if (!termQueries.containsKey(field)) {
            return;
        }
        // 解析传入的列表，进行字符串分割解析，确定属于哪一种产业领域
        final List<String> chainNodeIds = termQueries.get(field);
        termQueries.remove(field);
        if (CollUtil.isEmpty(chainNodeIds)) {
            return;
        }
        if (chainNodeIds.size() == 1 && chainNodeIds.contains(field)) {
            boolQuery.filter(QueryBuilders.existsQuery(field));
        } else {
            boolQuery.filter(QueryBuilders.termsQuery(field,
                    TreeBuildUtils.getChildrenIdOfNodes(
                            schemaGtcService.getTree(null), chainNodeIds)));
        }
    }

    public void generateDtcQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries) {
        final String field = "dtc";
        if (termQueries == null) {
            return;
        }
        if (!termQueries.containsKey(field)) {
            return;
        }
        // 解析传入的列表，进行字符串分割解析，确定属于哪一种产业领域
        final List<String> chainNodeIds = termQueries.get(field);
        termQueries.remove(field);
        if (CollUtil.isEmpty(chainNodeIds)) {
            return;
        }
        if (chainNodeIds.size() == 1 && chainNodeIds.contains(field)) {
            boolQuery.filter(QueryBuilders.existsQuery(field));
        } else {
            boolQuery.filter(QueryBuilders.termsQuery(field,
                    TreeBuildUtils.getChildrenIdOfNodes(
                            schemaDtcService.getTree(null), chainNodeIds)));
        }
    }

    /**
     * 产业节点要素查询
     */
    public EsPageResult queryByTermsAndKeyForIcd(final IcdDataQuery query) {
        final MultidimensionalQuery multidimensionalQuery = new MultidimensionalQuery();
        final String type = query.getType();
        if (StrUtil.isNotBlank(type)) {
            multidimensionalQuery.setIndex(EsIndexEnum.getEsIndexByType(type));
        }
        multidimensionalQuery.setType(type);
        multidimensionalQuery.setKeyword(query.getKeyword());
        multidimensionalQuery.setPageNum(query.getPageNum());
        multidimensionalQuery.setPageSize(query.getPageSize());
        multidimensionalQuery.setSort(query.getSort());
        multidimensionalQuery.setIsHighlight(query.getIsHighlight());
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 根据chainId判断使用哪种查询方式
        final String chainId = query.getChainId();
        if (StrUtil.isNotBlank(chainId) && !Objects.equals(chainId, "schema_icd")) {
            // 产业链节点查询：使用chain_node嵌套字段
            final List<String> nodeIdList = nodeService.getChildNodeIdList(chainId, query.getIcdNodeId());
            final BoolQueryBuilder chainNodeQuery = ChainNodeQueryUtil.buildChainNodeQuery(chainId, nodeIdList);
            boolQueryBuilder.filter(chainNodeQuery);
        } else {
            // ICD节点查询：使用传统的icd字段
            boolQueryBuilder.filter(QueryBuilders.termQuery("icd", query.getIcdNodeId()));
        }

        multidimensionalQuery.setBoolQuery(boolQueryBuilder);
        final Map<String, List<String>> termQueries = new HashMap<>(8);
        termQueries.put("labels", query.getLabels());
        termQueries.put("region", query.getRegion());
        termQueries.put("patent_type", query.getPatentTypeList());
        termQueries.put("status", query.getPatentStatusList());
        termQueries.put("financing_round_cal", query.getFinancingRoundList());
        multidimensionalQuery.setTermQueries(termQueries);
        // 获取对应的服务类
        return getService(multidimensionalQuery.getIndex())
                .getLibraryList(multidimensionalQuery);
    }

    public File companyDataExport(List<IndustryAnalysisCompanyListBO> list, final HttpServletResponse response) throws IOException {
        // 构建数据
        final List<OverseasCompanyListExport> dataList = new ArrayList<>();
        for (final IndustryAnalysisCompanyListBO sourceAsMap : list) {
            final OverseasCompanyListExport export = new OverseasCompanyListExport();
            export.setName(sourceAsMap.getName());
            export.setNation(sourceAsMap.getBelongArea());
            dataList.add(export);
        }
        return listExport(dataList, response, "海外企业明细导出表", OverseasCompanyListExport.class);
    }

    /**
     * 列表数据导出到response
     */
    public File listExport(final Collection<?> data,
                           final HttpServletResponse response,
                           final String name,
                           final Class classType) throws IOException {
        if (response == null) {
            return listExportToTempFile(data, name, classType);
        }
        // 导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        final Date today = new Date();
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        final String dateStr = sdf.format(today);
        final String excelName = name;
        final String fileName = excelName + "_" + dateStr + ".xlsx";
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        // 标题样式
        final WriteCellStyle headWriteCellStyle = ExcelStyleUtil.getHeadStyle();
        // 内容样式
        final WriteCellStyle contentWriteCellStyle = ExcelStyleUtil.getContentStyleNew();
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        final HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        final ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(horizontalCellStyleStrategy).registerWriteHandler(new CustomHandler()).build();
        final WriteSheet writeSheet = EasyExcel.writerSheet(0, name)
                .head(classType).build();
        excelWriter.write(data, writeSheet);
        excelWriter.finish();
        return null;
    }

    /**
     * 列表数据导出到临时文件
     */
    public File listExportToTempFile(final Collection<?> data,
                                     final String name,
                                     final Class classType) {
        final Date today = new Date();
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        final String dateStr = sdf.format(today);
        final String excelName = name;
        final String fileName = excelName + "_" + dateStr + ".xlsx";
        // 标题样式
        final WriteCellStyle headWriteCellStyle = ExcelStyleUtil.getHeadStyle();
        // 内容样式
        final WriteCellStyle contentWriteCellStyle = ExcelStyleUtil.getContentStyleNew();
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        final HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        final File tempFile = new File(getActualFilePath("temp", fileName));
        final ExcelWriter excelWriter = EasyExcel.write(tempFile)
                .registerWriteHandler(horizontalCellStyleStrategy).registerWriteHandler(new CustomHandler()).build();
        final WriteSheet writeSheet = EasyExcel.writerSheet(0, name)
                .head(classType).build();
        excelWriter.write(data, writeSheet);
        excelWriter.finish();
        return tempFile;
    }

    public File icdDataExport(final EsPageResult esPageResult, final HttpServletResponse response) throws IOException {
        return null;
    }

    public File ipcDataExport(final EsPageResult esPageResult, final HttpServletResponse response) throws IOException {
        return null;
    }

    public EsPageResult queryByTermsAndKeyForIpc(final IpcDataQuery query) {
        final String ipcField = determineIpcField(query.getIpc());
        final MultidimensionalQuery multidimensionalQuery = new MultidimensionalQuery();
        final String type = query.getType();
        if (StrUtil.isNotBlank(type)) {
            multidimensionalQuery.setIndex(EsIndexEnum.getEsIndexByType(type));
        }
        multidimensionalQuery.setType(type);
        multidimensionalQuery.setKeyword(query.getKeyword());
        multidimensionalQuery.setPageNum(query.getPageNum());
        multidimensionalQuery.setPageSize(query.getPageSize());
        multidimensionalQuery.setSort(query.getSort());
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        final TermQueryBuilder mainIpc = QueryBuilders.termQuery(ipcField, query.getIpc());
        // 如果是查询企业的话，需要先在专利表里聚合中对应的企业
        Map<String, Long> map = new HashMap<>();
        List<String> companyIds = new ArrayList<>();
        if (type.equals(EsIndexEnum.COMPANY.getIndex())) {
            final List<NameCountVO> companyIdCountList = elasticsearchHelper.getTermBucketsAggregation(EsIndexEnum.PATENT.getEsIndex(),
                    "applicants.id", QueryBuilders.boolQuery().filter(mainIpc), 100);
            if (CollUtil.isEmpty(companyIdCountList)) {
                return new EsPageResult();
            }
            companyIds = companyIdCountList.stream().map(NameCountVO::getName).collect(Collectors.toList());
            map = companyIdCountList.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));
            boolQueryBuilder.filter(QueryBuilders.termsQuery("id", map.keySet()));
            multidimensionalQuery.setPageNum(1);
            multidimensionalQuery.setPageSize(companyIdCountList.size());
            multidimensionalQuery.setSort(removeSortField(query.getSort(), "patentOwnershipCount"));
        } else if (type.equals(EsIndexEnum.PATENT.getIndex())) {
            boolQueryBuilder.filter(mainIpc);
        } else {
            throw new BusinessException("不支持的查询类型");
        }
        multidimensionalQuery.setBoolQuery(boolQueryBuilder);
        final Map<String, List<String>> termQueries = new HashMap<>(8);
        termQueries.put("labels", query.getLabels());
        termQueries.put("region", query.getRegion());
        termQueries.put("patent_type", query.getPatentTypeList());
        termQueries.put("status", query.getPatentStatusList());
        termQueries.put("financing_round_cal", query.getFinancingRoundList());
        multidimensionalQuery.setTermQueries(termQueries);
        // 如果是企业的话，需要手动进行分页
        if (type.equals(EsIndexEnum.COMPANY.getIndex())) {
            final EsPageResult libraryList = getService(EsIndexEnum.COMPANY.getIndex())
                    .getLibraryList(multidimensionalQuery);
            final List<Map<String, Object>> list = libraryList.getList();
            final List<Company> companies = companyService.listByIds(companyIds);
            final Map<String, Company> collect = companies.stream().collect(Collectors.toMap(Company::getId, Function.identity()));
            for (final Map<String, Object> item : list) {
                final Object tags = item.get("tags");
                final String id = (String) item.get("id");
                if (tags instanceof List) {
                    final Set<String> labelSet = new HashSet<>();
                    ((List) tags).forEach(
                            tag -> {
                                if (tag instanceof Map) {
                                    final Object tagObject = ((Map) tag).get("name");
                                    if (tagObject != null) {
                                        labelSet.add((String) tagObject);
                                    }
                                }
                            }
                    );
                    item.remove("tags");
                    item.put("labels", labelSet);
                    item.put("region", Constants.getBelongArea((String) item.get("province"), (String) item.get("city"), null));
                }
                final Company company = collect.get(id);
                if (company != null) {
                    item.put("legal_person", company.getLegalPerson());
                    item.put("regist_capi", company.getRegistCapi());
                }
                item.put("patentOwnershipCount", map.getOrDefault(id, 0L));
            }
            libraryList.setList(sortByCompanyIds(list, companyIds, parseSortString(query.getSort(), "patentOwnershipCount"), query.getPageNum(), query.getPageSize()));
            return libraryList;
        } else {
            return getService(multidimensionalQuery.getIndex())
                    .getLibraryList(multidimensionalQuery);
        }
    }


}
