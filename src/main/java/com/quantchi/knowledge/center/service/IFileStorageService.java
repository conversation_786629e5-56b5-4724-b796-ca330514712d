package com.quantchi.knowledge.center.service;

import java.io.File;
import java.io.InputStream;

/**
 * 统一文件存储服务接口
 * 
 * <AUTHOR>
 * @date 2025/6/5
 */
public interface IFileStorageService {
    
    /**
     * 上传文件
     * @param bucketName 桶名称
     * @param objectName 对象名称
     * @param inputStream 输入流
     * @param contentType 内容类型
     */
    void putObject(String bucketName, String objectName, InputStream inputStream, String contentType);
    
    /**
     * 上传文件
     * @param bucketName 桶名称
     * @param objectName 对象名称
     * @param file 文件对象
     */
    void putObject(String bucketName, String objectName, File file);
    
    /**
     * 下载文件
     * @param bucketName 桶名称
     * @param objectName 对象名称
     * @return 文件输入流
     */
    InputStream getObject(String bucketName, String objectName);
    
    /**
     * 下载文件到本地
     * @param bucketName 桶名称
     * @param objectName 对象名称
     * @param localFile 本地文件
     */
    void downloadObject(String bucketName, String objectName, File localFile);
    
    /**
     * 删除文件
     * @param bucketName 桶名称
     * @param objectName 对象名称
     */
    void removeObject(String bucketName, String objectName);
    
    /**
     * 检查存储桶是否存在，不存在则创建
     * @param bucketName 桶名称
     */
    void ensureBucketExists(String bucketName);
    
    /**
     * 获取默认存储桶名称
     * @return 存储桶名称
     */
    String getDefaultBucketName();
    
    /**
     * 生成预签名URL，用于临时访问
     * @param bucketName 桶名称
     * @param objectName 对象名称
     * @param contentType 内容类型
     * @param expiration 过期时间
     * @return 预签名URL字符串
     */
    String generatePresignedUrl(String bucketName, String objectName, String contentType, java.util.Date expiration);
}
