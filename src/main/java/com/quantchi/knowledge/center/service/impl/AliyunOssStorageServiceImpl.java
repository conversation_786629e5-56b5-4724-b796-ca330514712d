package com.quantchi.knowledge.center.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.quantchi.knowledge.center.config.properties.AliyunProperties;
import com.quantchi.knowledge.center.service.IFileStorageService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;

/**
 * 阿里云OSS存储服务实现
 * 
 * <AUTHOR>
 * @date 2025/6/5
 */
@Service("aliyunOssStorageService")
@Slf4j
public class AliyunOssStorageServiceImpl implements IFileStorageService {

    private final OSS ossClient;
    private final AliyunProperties ossProperties;

    public AliyunOssStorageServiceImpl(OSS ossClient, AliyunProperties ossProperties) {
        this.ossClient = ossClient;
        this.ossProperties = ossProperties;
    }

    @Override
    public void putObject(String bucketName, String objectName, InputStream inputStream, String contentType) {
        try {
            // 创建元数据
            ObjectMetadata metadata = new ObjectMetadata();
            if (contentType != null) {
                metadata.setContentType(contentType);
            }
            
            // 上传文件
            PutObjectRequest request = new PutObjectRequest(bucketName, objectName, inputStream, metadata);
            ossClient.putObject(request);
            log.info("阿里云OSS上传文件成功: bucket={}, object={}", bucketName, objectName);
        } catch (Exception e) {
            log.error("阿里云OSS上传文件失败", e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    @Override
    public void putObject(String bucketName, String objectName, File file) {
        try {
            ossClient.putObject(bucketName, objectName, file);
            log.info("阿里云OSS上传文件成功: bucket={}, object={}", bucketName, objectName);
        } catch (Exception e) {
            log.error("阿里云OSS上传文件失败", e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    @Override
    public InputStream getObject(String bucketName, String objectName) {
        try {
            return ossClient.getObject(bucketName, objectName).getObjectContent();
        } catch (Exception e) {
            log.error("阿里云OSS获取文件失败: bucket={}, object={}", bucketName, objectName, e);
            throw new RuntimeException("获取文件失败", e);
        }
    }

    @Override
    public void downloadObject(String bucketName, String objectName, File localFile) {
        try {
            ossClient.getObject(new GetObjectRequest(bucketName, objectName), localFile);
            log.info("阿里云OSS下载文件成功: bucket={}, object={}, localFile={}", bucketName, objectName, localFile.getPath());
        } catch (Exception e) {
            log.error("阿里云OSS下载文件失败", e);
            throw new RuntimeException("下载文件失败", e);
        }
    }

    @Override
    public void removeObject(String bucketName, String objectName) {
        try {
            ossClient.deleteObject(bucketName, objectName);
            log.info("阿里云OSS删除文件成功: bucket={}, object={}", bucketName, objectName);
        } catch (Exception e) {
            log.error("阿里云OSS删除文件失败", e);
            throw new RuntimeException("删除文件失败", e);
        }
    }

    @Override
    public void ensureBucketExists(String bucketName) {
        try {
            if (!ossClient.doesBucketExist(bucketName)) {
                ossClient.createBucket(bucketName);
                log.info("阿里云OSS创建存储桶成功: {}", bucketName);
            }
        } catch (Exception e) {
            log.error("阿里云OSS检查/创建存储桶失败", e);
            throw new RuntimeException("检查/创建存储桶失败", e);
        }
    }

    @Override
    public String getDefaultBucketName() {
        return ossProperties.getBucketName();
    }

    @Override
    public String generatePresignedUrl(String bucketName, String objectName, String contentType, java.util.Date expiration) {
        try {
            com.aliyun.oss.model.GeneratePresignedUrlRequest request = new com.aliyun.oss.model.GeneratePresignedUrlRequest(bucketName, objectName);
            request.setExpiration(expiration);
            // 设置HTTP方法为PUT，用于文件上传
            request.setMethod(com.aliyun.oss.HttpMethod.PUT);
            if (contentType != null) {
                request.setContentType(contentType);
            }

            java.net.URL signedUrl = ossClient.generatePresignedUrl(request);
            String urlString = signedUrl.toString();
            
            // 如果URL是http的，转换为https
            if (urlString != null && urlString.startsWith("http:")) {
                urlString = "https:" + urlString.substring(5);
            }
            
            log.info("阿里云OSS生成预签名URL成功: bucket={}, object={}, method=PUT, contentType={}, expiration={}",
                    bucketName, objectName, contentType, expiration);
            return urlString;
        } catch (Exception e) {
            log.error("阿里云OSS生成预签名URL失败", e);
            throw new RuntimeException("生成预签名URL失败", e);
        }
    }
}
