package com.quantchi.knowledge.center.service.impl;

import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.config.properties.MinioProperties;
import com.quantchi.knowledge.center.service.IFileStorageService;
import io.minio.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;


/**
 * MinIO存储服务实现
 * 
 * <AUTHOR>
 * @date 2025/6/5
 */
@Service("minioStorageService")
@Slf4j
public class MinioStorageServiceImpl implements IFileStorageService {
    
    private final MinioClient minioClient;
    private final MinioProperties minioProperties;
    
    public MinioStorageServiceImpl(MinioClient minioClient, MinioProperties minioProperties) {
        this.minioClient = minioClient;
        this.minioProperties = minioProperties;
    }
    
    @Override
    public void putObject(String bucketName, String objectName, InputStream inputStream, String contentType) {
        try {
            ensureBucketExists(bucketName);
            
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(inputStream, -1, 10485760) // 10MB 分片大小
                            .contentType(contentType != null ? contentType : "application/octet-stream")
                            .build()
            );
            
            log.info("MinIO上传文件成功: bucket={}, object={}", bucketName, objectName);
        } catch (Exception e) {
            log.error("MinIO上传文件失败", e);
            throw new RuntimeException("文件上传失败", e);
        }
    }
    
    @Override
    public void putObject(String bucketName, String objectName, File file) {
        try {
            ensureBucketExists(bucketName);
            
            minioClient.uploadObject(
                    UploadObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .filename(file.getAbsolutePath())
                            .build()
            );
            
            log.info("MinIO上传文件成功: bucket={}, object={}", bucketName, objectName);
        } catch (Exception e) {
            log.error("MinIO上传文件失败", e);
            throw new RuntimeException("文件上传失败", e);
        }
    }
    
    @Override
    public InputStream getObject(String bucketName, String objectName) {
        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
        } catch (Exception e) {
            log.error("MinIO获取文件失败: bucket={}, object={}", bucketName, objectName, e);
            throw new RuntimeException("获取文件失败", e);
        }
    }
    
    @Override
    public void downloadObject(String bucketName, String objectName, File localFile) {
        try {
            // 如果目标文件已存在，先删除
            if (localFile.exists()) {
                boolean deleted = localFile.delete();
                if (!deleted) {
                    log.warn("无法删除已存在的目标文件: {}", localFile.getAbsolutePath());
                    // 如果无法删除，尝试重命名原文件
                    File renamedFile = new File(localFile.getAbsolutePath() + "." + System.currentTimeMillis());
                    if (localFile.renameTo(renamedFile)) {
                        log.info("已存在的目标文件已重命名: {} -> {}", localFile.getAbsolutePath(), renamedFile.getAbsolutePath());
                    } else {
                        throw new BusinessException("无法删除或重命名已存在的目标文件: " + localFile.getAbsolutePath());
                    }
                } else {
                    log.info("已删除已存在的目标文件: {}", localFile.getAbsolutePath());
                }
            }

            minioClient.downloadObject(
                    DownloadObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .filename(localFile.getAbsolutePath())
                            .build()
            );
            
            log.info("MinIO下载文件成功: bucket={}, object={}, localFile={}", bucketName, objectName, localFile.getPath());
        } catch (Exception e) {
            log.error("MinIO下载文件失败", e);
            throw new RuntimeException("下载文件失败", e);
        }
    }
    
    @Override
    public void removeObject(String bucketName, String objectName) {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
            
            log.info("MinIO删除文件成功: bucket={}, object={}", bucketName, objectName);
        } catch (Exception e) {
            log.error("MinIO删除文件失败", e);
            throw new RuntimeException("删除文件失败", e);
        }
    }
    
    @Override
    public void ensureBucketExists(String bucketName) {
        try {
            boolean bucketExists = minioClient.bucketExists(
                    BucketExistsArgs.builder()
                            .bucket(bucketName)
                            .build()
            );
            
            if (!bucketExists) {
                minioClient.makeBucket(
                        MakeBucketArgs.builder()
                                .bucket(bucketName)
                                .build()
                );
                
                log.info("MinIO创建存储桶成功: {}", bucketName);
            }
        } catch (Exception e) {
            log.error("MinIO检查/创建存储桶失败", e);
            throw new RuntimeException("检查/创建存储桶失败", e);
        }
    }
    
    @Override
    public String getDefaultBucketName() {
        return minioProperties.getBucketName();
    }
    
    @Override
    public String generatePresignedUrl(String bucketName, String objectName, String contentType, java.util.Date expiration) {
        try {
            // 计算过期时长（秒）
            long expirySeconds = (expiration.getTime() - System.currentTimeMillis()) / 1000;
            if (expirySeconds < 1) {
                expirySeconds = 3600; // 默认1小时
            }
            
            // 构建请求参数
            io.minio.http.Method method = io.minio.http.Method.GET;
            String urlString = minioClient.getPresignedObjectUrl(
                    io.minio.GetPresignedObjectUrlArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .method(method)
                            .expiry((int) expirySeconds)
                            .build());
            
            // 如果URL是http的，转换为https
            if (urlString != null && urlString.startsWith("http:")) {
                urlString = "https:" + urlString.substring(5);
            }
            
            log.info("MinIO生成预签名URL成功: bucket={}, object={}", bucketName, objectName);
            return urlString;
        } catch (Exception e) {
            log.error("MinIO生成预签名URL失败", e);
            throw new RuntimeException("生成预签名URL失败", e);
        }
    }
}
