package com.quantchi.knowledge.center.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quantchi.knowledge.center.bean.bo.IndustryLinkProsperityBO;
import com.quantchi.knowledge.center.bean.bo.ReportDownloadBO;
import com.quantchi.knowledge.center.bean.bo.ReportGenerateQuery;
import com.quantchi.knowledge.center.bean.entity.*;
import com.quantchi.knowledge.center.bean.enums.DownloadTypeEnum;
import com.quantchi.knowledge.center.bean.enums.ReportAuditStatusEnum;
import com.quantchi.knowledge.center.bean.enums.ReportDisplayStatusEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.model.IdNameModel;
import com.quantchi.knowledge.center.bean.system.bo.SysReportBatchDeleteBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportDownloadBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportSubmitBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportUploadBO;
import com.quantchi.knowledge.center.bean.system.query.ReportQuery;
import com.quantchi.knowledge.center.bean.system.vo.SysReportBatchDeleteResultVO;
import com.quantchi.knowledge.center.bean.system.vo.SysReportDetailVO;
import com.quantchi.knowledge.center.bean.system.vo.SysReportPageVO;
import com.quantchi.knowledge.center.bean.vo.SysFileVO;
import com.quantchi.knowledge.center.config.ModelParamConfig;
import com.quantchi.knowledge.center.config.properties.AliyunProperties;
import com.quantchi.knowledge.center.constants.Constant;
import com.quantchi.knowledge.center.dao.mysql.ProsperityMapper;
import com.quantchi.knowledge.center.dao.mysql.SysFileMapper;
import com.quantchi.knowledge.center.dao.mysql.SysReportMapper;
import com.quantchi.knowledge.center.dao.mysql.SysUserMapper;
import com.quantchi.knowledge.center.service.*;
import com.quantchi.knowledge.center.util.HttpCallClient;
import com.quantchi.knowledge.center.util.LocalFileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.quantchi.knowledge.center.config.MyBatisPlusConfig.DYNAMIC_TABLE_NAME_THREAD_LOCAL;
import static com.quantchi.knowledge.center.controller.IndustryLinkAnalysisController.SCHEMA_ICD;
import static com.quantchi.knowledge.center.util.LocalFileUtil.getActualFilePath;

/**
 * <p>
 * 报告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysReportServiceImpl extends ServiceImpl<SysReportMapper, SysReport> implements ISysReportService {

    @Value("${file.preUrl}")
    private String filePreUrl;

    @Value("${file.path}")
    private String filePath;

    @Value("${report.url}")
    private String reportGenerateUrl;

    private final ModelParamConfig modelParamConfig;

    private final SysUserMapper sysUserMapper;
    private final SysFileMapper sysFileMapper;
    private final SysReportMapper sysReportMapper;
    private final IDmDivisionService dmDivisionService;
    private final IIndustryChainService industryChainService;
    private final SchemaIcdService schemaIcdService;
    private final ProsperityMapper prosperityMapper;
    private final ISysFileService sysFileService;
    private final ThreadPoolTaskExecutor docExecutor;
    private final ISysUserDownloadService sysUserDownloadService;
    private final ISysReportTagRelationService sysReportTagRelationService;
    private final AliyunProperties ossProperties;
    private final IFileStorageService fileStorageService;


    /**
     * 上传报告
     */
    @Override
    public Boolean uploadReport(final SysReportUploadBO reportUploadBO) {
        final String fileKey = reportUploadBO.getFileKey();
        final String originalFileName = reportUploadBO.getOriginalFileName();
        final Long reportId = reportUploadBO.getReportId();
        if (reportId != null) {
            return editReport(reportUploadBO);
        }
        // 使用临时文件检查文件是否存在
        File tempFile = null;
        try {
            tempFile = File.createTempFile("report_check_", ".tmp");
            fileStorageService.downloadObject(ossProperties.getBucketName(), fileKey, tempFile);
        } catch (IOException e) {
            log.error("创建临时文件失败", e);
            throw new BusinessException("系统错误，请稍后重试");
        } catch (Exception e) {
            // 如果下载失败，说明文件不存在
            log.error("文件不存在或下载失败", e);
            throw new BusinessException("文件不存在");
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
        final SysReport sysReport = new SysReport();
        final long loginId = StpUtil.getLoginIdAsLong();
        sysReport.setTitle(reportUploadBO.getTitle());
        sysReport.setAuthor(reportUploadBO.getAuthor());
        sysReport.setIssuingAgency(reportUploadBO.getIssuingAgency());
        sysReport.setIntroduce(reportUploadBO.getIntroduce());
        sysReport.setPublishDate(new Date());
        sysReport.setDisplayStatus(reportUploadBO.getIsPublic() ? ReportDisplayStatusEnum.PUBLICY.getId() : ReportDisplayStatusEnum.NOT_PUBLIC.getId());
        sysReport.setAuditStatus(ReportAuditStatusEnum.ONE.getId());
        sysReport.setOwnerId(loginId);
        sysReport.setOwnerName(sysUserMapper.selectById(loginId).getMemberNickName());
        final boolean save = save(sysReport);
        docExecutor.execute(() -> {
            final Long fileId;
            final String previewUrl;
            final String downloadUrl;
            final SysFileVO sysFileVO;
            try {
                sysFileVO = sysFileService.dealWithFile(originalFileName, fileKey, "report", null);
            } catch (Exception e) {
                log.error("文件处理失败", e);
                return;
            }
            fileId = sysFileVO.getFileId();
            previewUrl = sysFileVO.getPreviewUrl();
            downloadUrl = sysFileVO.getUrl();

            if (fileId != null) {
                final SysFile sysFile = sysFileMapper.selectById(fileId);
                if (sysFile != null) {
                    final String fileName = sysFile.getFileName();
                    final String[] split = fileName.split("\\.");
                    if (split.length > 1) {
                        if (split[1].contains("pdf")) {
                            sysReport.setHomePageImageUrl(sysFileVO.getHomePageUrl());
                        } else if (!split[1].contains("xlx")) {
                            sysReport.setHomePageImageUrl(filePreUrl + "/preview/" + split[0] + "/0.jpg");
                        }
                    }
                }
            }
            sysReport.setFileId(fileId);
            sysReport.setPreviewUrl(previewUrl);

            // 解析文件内容并生成摘要
            generateReportSummary(sysReport, fileKey, originalFileName);

            // 保存报告标签
            final List<IdNameModel> reportTagList = reportUploadBO.getTagList();
            if (CollUtil.isNotEmpty(reportTagList)) {
                final List<SysReportTagRelation> sysReportTagRelationList = reportTagList.stream().map(reportTag -> {
                    final SysReportTagRelation sysReportTagRelation = new SysReportTagRelation();
                    sysReportTagRelation.setReportId(sysReport.getId());
                    sysReportTagRelation.setTagId(reportTag.getId());
                    sysReportTagRelation.setTagName(reportTag.getName());
                    sysReportTagRelation.setTagType(reportTag.getType());
                    return sysReportTagRelation;
                }).collect(Collectors.toList());
                sysReportTagRelationService.saveBatch(sysReportTagRelationList);
            }
            sysReport.setDownloadUrl(downloadUrl + "&reportId=" + sysReport.getId());
            sysReportMapper.updateById(sysReport);
        });
        return save;
    }

    @Override
    public Boolean editReport(final SysReportUploadBO reportUploadBO) {
        final Long reportId = reportUploadBO.getReportId();
        final SysReport sysReport = this.getById(reportId);
        final long loginId = StpUtil.getLoginIdAsLong();
        final Long ownerId = sysReport.getOwnerId();
        if (!Objects.equals(ownerId, loginId)) {
            throw new BusinessException("只能修改自己的报告");
        }
        sysReport.setTitle(reportUploadBO.getTitle());
        sysReport.setAuthor(reportUploadBO.getAuthor());
        sysReport.setIssuingAgency(reportUploadBO.getIssuingAgency());
        sysReport.setIntroduce(reportUploadBO.getIntroduce());
        final boolean save = updateById(sysReport);
        // 保存报告标签
        final List<IdNameModel> reportTagList = reportUploadBO.getTagList();
        sysReportTagRelationService.remove(Wrappers.<SysReportTagRelation>lambdaQuery()
                .eq(SysReportTagRelation::getReportId, reportId));
        if (CollUtil.isNotEmpty(reportTagList)) {
            final List<SysReportTagRelation> sysReportTagRelationList = reportTagList.stream().map(reportTag -> {
                final SysReportTagRelation sysReportTagRelation = new SysReportTagRelation();
                sysReportTagRelation.setReportId(sysReport.getId());
                sysReportTagRelation.setTagId(reportTag.getId());
                sysReportTagRelation.setTagName(reportTag.getName());
                sysReportTagRelation.setTagType(reportTag.getType());
                return sysReportTagRelation;
            }).collect(Collectors.toList());
            sysReportTagRelationService.saveBatch(sysReportTagRelationList);
        }
        return save;
    }

    /**
     * 我的报告列表
     */
    @Override
    public PageInfo<SysReportPageVO> myReportList(final ReportQuery reportQuery) {
        final String keyword = reportQuery.getKeyword();
        final Integer pageNum = reportQuery.getPageNum();
        final Integer pageSize = reportQuery.getPageSize();
        PageHelper.startPage(pageNum, pageSize);
        final List<SysReport> list = this.list(Wrappers.<SysReport>lambdaQuery()
                .like(CharSequenceUtil.isNotBlank(keyword), SysReport::getTitle, keyword)
                .ne(SysReport::getDisplayStatus, ReportDisplayStatusEnum.DELETED.getId())
                .eq(SysReport::getOwnerId, StpUtil.getLoginIdAsLong())
                .eq(SysReport::getIsValid, 1)
                .orderByDesc(SysReport::getPublishDate));
        return new PageInfo<>(list).convert(SysReportPageVO::build);
    }


    /**
     * 报告集市列表
     */
    @Override
    public PageInfo<SysReportPageVO> reportMarket(final ReportQuery reportQuery) {
        final String keyword = reportQuery.getKeyword();
        final Integer pageNum = reportQuery.getPageNum();
        final Integer pageSize = reportQuery.getPageSize();
        final List<IdNameModel> tagList = reportQuery.getTagList();
        final List<SysReport> list;
        PageHelper.startPage(pageNum, pageSize);
        if (CollUtil.isNotEmpty(tagList)) {
            final List<IdNameModel> xinxingTagList = tagList.stream().filter(item -> item.getType().equals("新兴产业")).collect(Collectors.toList());
            final List<IdNameModel> weilaiTagList = tagList.stream().filter(item -> item.getType().equals("未来产业")).collect(Collectors.toList());
            list = sysReportMapper.getReportList(keyword, xinxingTagList, weilaiTagList);
        } else {
            // 修改查询逻辑，支持基于标题、产业、摘要、关键词进行检索
            final LambdaQueryWrapper<SysReport> queryWrapper = Wrappers.<SysReport>lambdaQuery()
                    .eq(SysReport::getDisplayStatus, ReportDisplayStatusEnum.PUBLICY.getId())
                    .eq(SysReport::getIsValid, 1)
                    .orderByDesc(SysReport::getPublishDate);

            // 如果有关键词，使用增强的搜索逻辑
            if (CharSequenceUtil.isNotBlank(keyword)) {
                // 先查询包含关键词的产业标签对应的报告ID
                final List<SysReportTagRelation> matchingTagRelations = sysReportTagRelationService.list(
                        Wrappers.<SysReportTagRelation>lambdaQuery()
                                .like(SysReportTagRelation::getTagName, keyword)
                );
                final List<Long> reportIdsFromTags = matchingTagRelations.stream()
                        .map(SysReportTagRelation::getReportId)
                        .distinct()
                        .collect(Collectors.toList());

                queryWrapper.and(wrapper -> {
                    // 搜索标题、介绍、摘要
                    wrapper.like(SysReport::getTitle, keyword)
                            .or()
                            .like(SysReport::getIntroduce, keyword)
                            .or()
                            .like(SysReport::getSummarize, keyword);

                    // 如果有匹配的产业标签，也包含这些报告
                    if (CollUtil.isNotEmpty(reportIdsFromTags)) {
                        wrapper.or().in(SysReport::getId, reportIdsFromTags);
                    }
                });
            }

            list = sysReportMapper.selectList(queryWrapper);
        }

        // 批量查询产业标签信息并转换为VO
        return convertToPageVOWithTags(new PageInfo<>(list));
    }

    /**
     * 批量构建包含产业标签的报告页面VO
     *
     * @param pageInfo 分页信息
     * @return 包含产业标签的报告页面VO分页信息
     */
    private PageInfo<SysReportPageVO> convertToPageVOWithTags(final PageInfo<SysReport> pageInfo) {
        final List<SysReport> reportList = pageInfo.getList();
        if (CollUtil.isEmpty(reportList)) {
            return new PageInfo<>(Collections.emptyList());
        }

        // 批量查询所有报告的产业标签
        final List<Long> reportIds = reportList.stream()
                .map(SysReport::getId)
                .collect(Collectors.toList());

        final List<SysReportTagRelation> allTagRelations = sysReportTagRelationService.list(
                Wrappers.<SysReportTagRelation>lambdaQuery()
                        .in(SysReportTagRelation::getReportId, reportIds)
        );

        // 按报告ID分组标签
        final Map<Long, List<String>> reportTagsMap = allTagRelations.stream()
                .filter(relation -> CharSequenceUtil.isNotBlank(relation.getTagName()))
                .collect(Collectors.groupingBy(
                        SysReportTagRelation::getReportId,
                        Collectors.mapping(SysReportTagRelation::getTagName, Collectors.toList())
                ));

        // 转换为VO
        final List<SysReportPageVO> voList = reportList.stream()
                .map(report -> {
                    final List<String> industryTags = reportTagsMap.getOrDefault(report.getId(), Collections.emptyList());
                    return SysReportPageVO.build(report, industryTags);
                })
                .collect(Collectors.toList());

        // 构建新的分页信息
        final PageInfo<SysReportPageVO> result = new PageInfo<>(voList);
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setPages(pageInfo.getPages());
        result.setSize(pageInfo.getSize());
        result.setStartRow(pageInfo.getStartRow());
        result.setEndRow(pageInfo.getEndRow());
        result.setHasNextPage(pageInfo.isHasNextPage());
        result.setHasPreviousPage(pageInfo.isHasPreviousPage());
        result.setIsFirstPage(pageInfo.isIsFirstPage());
        result.setIsLastPage(pageInfo.isIsLastPage());
        result.setNavigatePages(pageInfo.getNavigatePages());
        result.setNavigatepageNums(pageInfo.getNavigatepageNums());
        result.setNavigateFirstPage(pageInfo.getNavigateFirstPage());
        result.setNavigateLastPage(pageInfo.getNavigateLastPage());
        result.setPrePage(pageInfo.getPrePage());
        result.setNextPage(pageInfo.getNextPage());

        return result;
    }

    /**
     * 公开报告
     */
    @Override
    public Boolean publicReport(final Long reportId) {
        final SysReport sysReport = validAndGetReport(reportId);
        sysReport.setDisplayStatus(ReportDisplayStatusEnum.PUBLICY.getId());
        return updateById(sysReport);
    }

    /**
     * 下架报告
     */
    @Override
    public Boolean unShelfReport(final Long reportId) {
        final SysReport sysReport = validAndGetReport(reportId);
        sysReport.setDisplayStatus(ReportDisplayStatusEnum.UNSHELF.getId());
        return updateById(sysReport);
    }

    /**
     * 删除报告
     */
    @Override
    public Boolean deleteReport(final Long reportId) {
        final SysReport sysReport = validAndGetReport(reportId);

        // 检查报告状态，只有未提交（未公开）和已下架的报告能被删除
        final Integer displayStatus = sysReport.getDisplayStatus();
        if (!ReportDisplayStatusEnum.NOT_PUBLIC.getId().equals(displayStatus)
            && !ReportDisplayStatusEnum.UNSHELF.getId().equals(displayStatus)) {
            throw new BusinessException("只有未提交和已下架的报告可以删除，已公开的报告请先下架后再删除");
        }

        sysReport.setDisplayStatus(ReportDisplayStatusEnum.DELETED.getId());
        return updateById(sysReport);
    }

    /**
     * 批量删除报告
     */
    @Override
    public SysReportBatchDeleteResultVO batchDeleteReport(final SysReportBatchDeleteBO batchDeleteBO) {
        final List<Long> reportIds = batchDeleteBO.getReportIds();
        final SysReportBatchDeleteResultVO result = new SysReportBatchDeleteResultVO();
        final List<Long> successReportIds = new ArrayList<>();
        final List<SysReportBatchDeleteResultVO.FailureInfo> failureInfos = new ArrayList<>();

        // 遍历每个报告ID，复用单个删除的逻辑
        for (final Long reportId : reportIds) {
            try {
                // 复用单个删除的验证和删除逻辑
                final Boolean deleteResult = deleteReport(reportId);
                if (deleteResult) {
                    successReportIds.add(reportId);
                } else {
                    failureInfos.add(new SysReportBatchDeleteResultVO.FailureInfo(
                        reportId,
                        "未知",
                        "删除操作返回失败"
                    ));
                }
            } catch (final BusinessException e) {
                // 获取报告标题用于错误信息
                String reportTitle = "未知";
                try {
                    final SysReport sysReport = this.getById(reportId);
                    if (sysReport != null) {
                        reportTitle = sysReport.getTitle();
                    }
                } catch (final Exception ex) {
                    // 忽略获取标题时的异常
                }

                failureInfos.add(new SysReportBatchDeleteResultVO.FailureInfo(
                    reportId,
                    reportTitle,
                    e.getMessage()
                ));
            } catch (final Exception e) {
                // 获取报告标题用于错误信息
                String reportTitle = "未知";
                try {
                    final SysReport sysReport = this.getById(reportId);
                    if (sysReport != null) {
                        reportTitle = sysReport.getTitle();
                    }
                } catch (final Exception ex) {
                    // 忽略获取标题时的异常
                }

                failureInfos.add(new SysReportBatchDeleteResultVO.FailureInfo(
                    reportId,
                    reportTitle,
                    "系统异常：" + e.getMessage()
                ));
            }
        }

        // 设置结果
        result.setSuccessCount(successReportIds.size());
        result.setFailureCount(failureInfos.size());
        result.setSuccessReportIds(successReportIds);
        result.setFailureInfos(failureInfos);

        return result;
    }

    @Override
    public Map<String, Object> getOption() {
        final Map<String, Object> mapOption = new HashMap<>();
        mapOption.put("选择产业", industryChainService.list(Wrappers.<Knowledge>lambdaQuery()
                .eq(Knowledge::getIsValid, 1)));
        final List<DmDivision> region = new ArrayList<>();
        final QueryWrapper<DmDivision> queryWrapper = Wrappers.<DmDivision>query()
                .in("level", 1, 2);
        final List<DmDivision> list = dmDivisionService.list(queryWrapper);
        final Map<String, DmDivision> map = new HashMap<>();
        for (final DmDivision division : list) {
            map.put(division.getId(), division);
        }
        final List<DmDivision> result = new ArrayList<>();
        for (final DmDivision division : list) {
            if ("division/000000".equals(division.getParentId())) {
                result.add(division);
            } else {
                final DmDivision parent = map.get(division.getParentId());
                parent.getChildren().add(division);
            }
        }

        final DmDivision country = new DmDivision();
        country.setName("全国");
        country.setChildren(result);
        country.setId("000");
        region.add(country);
        final DmDivision economicCircle = new DmDivision();
        economicCircle.setName("经济圈");
        economicCircle.setChildren(Constant.ECONOMIC_CIRCLE_DIVISION);
        economicCircle.setId("001");
        region.add(economicCircle);
        mapOption.put("选择区域", region);
        return mapOption;
    }

    /**
     * 报告详情
     */
    @Override
    public SysReportDetailVO reportInfo(final Long reportId) {
        final SysReport sysReport = this.getById(reportId);
        sysReport.setClickTimes(sysReport.getClickTimes() + 1);
        updateById(sysReport);
        final Long fileId = sysReport.getFileId();
        final SysFile sysFile = sysFileService.getById(fileId);
        final List<SysReportTagRelation> reportTagRelationList = sysReportTagRelationService.list(Wrappers.<SysReportTagRelation>lambdaQuery()
                .eq(SysReportTagRelation::getReportId, reportId));
        return SysReportDetailVO.build(sysReport, sysFile, reportTagRelationList);
    }

    /**
     * AI报告下载，调用研究院接口
     *
     * @param bo
     * @return
     */
    @Override
    public String downloadAIReport(final ReportDownloadBO bo, final Long sysUserDownloadId, final long userId) {
        final String chainId = bo.getChainId();
        if (CharSequenceUtil.isNotBlank(chainId)) {
            final Knowledge knowledge = industryChainService.getById(chainId);
            final ReportGenerateQuery query = new ReportGenerateQuery();
            query.setInstance_id(knowledge.getId());
            query.setInstance_name(knowledge.getName());
            query.setInstance_type("industry");
            bo.setQuery(query);
        }
        final String icdNodeId = bo.getNodeId();
        if (CharSequenceUtil.isNotBlank(icdNodeId)) {
            final SchemaIcd schemaIcd = schemaIcdService.getById(icdNodeId);
            DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(SCHEMA_ICD);
            final List<IndustryLinkProsperityBO> prosperityForIcd = prosperityMapper.getProsperityForIcd(icdNodeId, 2024);
            if (prosperityForIcd.isEmpty()) {
                throw new BusinessException("暂无报告");
            }
            final ReportGenerateQuery query = new ReportGenerateQuery();
            query.setInstance_id(schemaIcd.getId());
            query.setInstance_name(schemaIcd.getName());
            query.setInstance_type("icd");
            bo.setQuery(query);
        }
        if (bo.getQuery() != null) {
            final ReportGenerateQuery query = bo.getQuery();
            final String instanceType = query.getInstance_type();
            final String instanceName = query.getInstance_name();
            // 判断文件是否为当天的，不是的话需要调接口生成
            String fileKey = query.getInstance_id() + "_" + instanceType + "_report.docx";
            try {
                // 使用临时文件获取文件元数据
                File tempFile = null;
                try {
                    tempFile = File.createTempFile("meta_check_", ".tmp");
                    fileStorageService.downloadObject(ossProperties.getBucketName(), fileKey, tempFile);
                    
                    // 获取文件最后修改时间
                    final Date lastModified = new Date(tempFile.lastModified());
                    
                    // 如果文件不是当天的，重新生成
                    if (lastModified.before(DateUtil.beginOfDay(new Date()))) {
                        fileKey = HttpCallClient.getStringDataFromUrl(reportGenerateUrl, JSON.toJSONString(query));
                    }
                } catch (IOException e) {
                    log.error("创建临时文件失败", e);
                    // 创建临时文件失败时，尝试重新生成文件
                    fileKey = HttpCallClient.getStringDataFromUrl(reportGenerateUrl, JSON.toJSONString(query));
                } finally {
                    // 删除临时文件
                    if (tempFile != null && tempFile.exists()) {
                        tempFile.delete();
                    }
                }
            } catch (final Exception e) {
                log.error("文件不存在，重新生成文件");
                fileKey = HttpCallClient.getStringDataFromUrl(reportGenerateUrl, JSON.toJSONString(query));
            }
            if (CharSequenceUtil.isBlank(fileKey)) {
                throw new BusinessException("暂无报告");
            }
            final String fileUrl = filePreUrl + filePath + "?fileName=" + fileKey + "&originFileName=" + instanceName + "." + fileKey.split("\\.")[1];
            final String sectorName;
            switch (instanceType) {
                case "industry":
                    sectorName = "产业链分析洞察报告-" + instanceName;
                    break;
                case "icd":
                    sectorName = "产业画像分析报告-" + instanceName;
                    break;
                case "technology":
                    sectorName = "技术画像分析报告-" + instanceName;
                    break;
                case "company":
                    sectorName = "企业画像分析报告-" + instanceName;
                    break;
                default:
                    return "暂无报告";
            }
            final String fileName = instanceName + "." + fileKey.split("\\.")[1];
            // 保存当次的记录
            final File tempFile = new File(getActualFilePath("temp", fileName));
            HttpUtil.downloadFile(fileUrl, tempFile);
            final String fileExtension = LocalFileUtil.getFileExtension(fileName);
            final SysFileVO sysFileVO;
            try {
                sysFileVO = sysFileService.uploadFile(tempFile, fileName, fileExtension);
                if (sysUserDownloadId != null) {
                    sysUserDownloadService.updateDownloadRecord(sysFileVO, sectorName, userId, sysUserDownloadId, 1);
                } else {
                    sysUserDownloadService.saveDownloadRecord(sysFileVO, sectorName, userId);
                }
            } catch (final Exception e) {
                log.error("文件上传失败", e);
            }
            tempFile.delete();
            return fileUrl;
        }
        throw new BusinessException("暂无报告");
    }

    /**
     * 提交报告
     */
    @Override
    public Boolean submitReport(final SysReportSubmitBO reportSubmitBO) {
        final Long reportId = reportSubmitBO.getReportId();
        final SysReport sysReport = validAndGetReport(reportId);

        // 更新报告基本信息
        if (CharSequenceUtil.isNotBlank(reportSubmitBO.getTitle())) {
            sysReport.setTitle(reportSubmitBO.getTitle());
        }
        if (CharSequenceUtil.isNotBlank(reportSubmitBO.getAuthor())) {
            sysReport.setAuthor(reportSubmitBO.getAuthor());
        }
        if (CharSequenceUtil.isNotBlank(reportSubmitBO.getIssuingAgency())) {
            sysReport.setIssuingAgency(reportSubmitBO.getIssuingAgency());
        }

        // 设置报告状态为已公开
        sysReport.setDisplayStatus(ReportDisplayStatusEnum.PUBLICY.getId());

        final boolean updateResult = updateById(sysReport);

        // 更新报告标签
        final List<IdNameModel> tagList = reportSubmitBO.getTagList();
        if (CollUtil.isNotEmpty(tagList)) {
            // 先删除原有标签
            sysReportTagRelationService.remove(Wrappers.<SysReportTagRelation>lambdaQuery()
                    .eq(SysReportTagRelation::getReportId, reportId));

            // 保存新标签
            final List<SysReportTagRelation> sysReportTagRelationList = tagList.stream().map(reportTag -> {
                final SysReportTagRelation sysReportTagRelation = new SysReportTagRelation();
                sysReportTagRelation.setReportId(reportId);
                sysReportTagRelation.setTagId(reportTag.getId());
                sysReportTagRelation.setTagName(reportTag.getName());
                sysReportTagRelation.setTagType(reportTag.getType());
                return sysReportTagRelation;
            }).collect(Collectors.toList());
            sysReportTagRelationService.saveBatch(sysReportTagRelationList);
        }

        return updateResult;
    }

    /**
     * 下载报告
     */
    @Override
    public Integer downloadReport(final SysReportDownloadBO reportDownloadBO) throws Exception {
        final Long reportId = reportDownloadBO.getReportId();
        final SysReport sysReport = this.getById(reportId);

        if (sysReport == null) {
            throw new BusinessException("报告不存在");
        }

        final long userId = StpUtil.getLoginIdAsLong();

        // 生成下载记录
        final SysUserDownload sysUserDownload = new SysUserDownload();
        sysUserDownload.setUserId(userId);
        sysUserDownload.setModule("报告中心-报告下载");
        sysUserDownload.setName(sysReport.getTitle());
        sysUserDownload.setStatus(0); // 0-生成中
        sysUserDownload.setType(DownloadTypeEnum.REPORT_DOWNLOAD.getId());
        sysUserDownload.setCreateTime(new Date());
        sysUserDownload.setIsValid(1);
        sysUserDownloadService.save(sysUserDownload);

        final Long sysUserDownloadId = sysUserDownload.getId();

        // 异步处理文件下载和保存
        docExecutor.execute(() -> {
            try {
                // 获取报告文件信息
                final Long fileId = sysReport.getFileId();
                if (fileId == null) {
                    throw new BusinessException("报告文件不存在");
                }

                final SysFile sysFile = sysFileMapper.selectById(fileId);
                if (sysFile == null) {
                    throw new BusinessException("报告文件不存在");
                }

                // 创建SysFileVO对象
                final SysFileVO sysFileVO = new SysFileVO();
                sysFileVO.setFileId(fileId);
                sysFileVO.setFileName(sysFile.getFileName());
                sysFileVO.setUrl(sysFile.getUrl());

                // 更新下载记录
                sysUserDownloadService.updateDownloadRecord(
                    sysFileVO,
                    "报告下载-" + sysReport.getTitle(),
                    userId,
                    sysUserDownloadId,
                    DownloadTypeEnum.REPORT_DOWNLOAD.getId()
                );

                // 更新报告下载次数
                sysReport.setDownloadTimes((sysReport.getDownloadTimes() == null ? 0 : sysReport.getDownloadTimes()) + 1);
                updateById(sysReport);

            } catch (final Exception e) {
                log.error("报告下载失败", e);
                // 更新下载记录状态为失败
                sysUserDownload.setStatus(2); // 2-生成失败
                sysUserDownloadService.updateById(sysUserDownload);
            }
        });

        return 1;
    }

    private SysReport validAndGetReport(final Long reportId) {
        final SysReport sysReport = this.getById(reportId);
        final long loginId = StpUtil.getLoginIdAsLong();
        if (sysReport == null) {
            throw new BusinessException("报告不存在");
        }
        if (sysReport.getOwnerId() != loginId) {
            throw new BusinessException("没有权限");
        }
        return sysReport;
    }

    /**
     * 生成报告摘要
     *
     * @param sysReport 报告对象
     * @param fileKey 文件key
     * @param originalFileName 原始文件名
     */
    private void generateReportSummary(final SysReport sysReport, final String fileKey, final String originalFileName) {
        try {
            // 下载文件到临时目录
            final File tempFile = File.createTempFile("report_analysis_", getFileExtension(originalFileName));
            fileStorageService.downloadObject(ossProperties.getBucketName(), fileKey, tempFile);

            // 解析文件内容
            final String fileContent = analysisFileContent(tempFile);

            if (CharSequenceUtil.isNotBlank(fileContent)) {
                // 调用大模型生成摘要
                final String summary = generateSummaryByAI(fileContent);
                if (CharSequenceUtil.isNotBlank(summary)) {
                    sysReport.setSummarize(summary);
                    log.info("报告 {} 摘要生成成功: {}", sysReport.getTitle(), summary);
                } else {
                    log.warn("报告 {} 摘要生成失败，大模型返回空内容", sysReport.getTitle());
                }
            } else {
                log.warn("报告 {} 文件内容解析为空，无法生成摘要", sysReport.getTitle());
            }

            // 删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }

        } catch (final Exception e) {
            log.error("生成报告摘要失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析文件内容
     *
     * @param file 文件
     * @return 文件内容
     * @throws IOException IO异常
     */
    private String analysisFileContent(final File file) throws IOException {
        final String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".docx") || fileName.endsWith(".doc")) {
            return parseWordDocument(file);
        } else if (fileName.endsWith(".pdf")) {
            return readPdfFile(file);
        } else {
            throw new BusinessException("不支持的文件类型");
        }
    }

    /**
     * 调用大模型生成摘要
     *
     * @param fileContent 文件内容
     * @return 摘要
     */
    private String generateSummaryByAI(final String fileContent) {
        try {
            // 构建提示词
            final String prompt = buildSummaryPrompt(fileContent);

            // 调用智谱AI
            return zhipuSyncCall(prompt);

        } catch (final Exception e) {
            log.error("调用大模型生成摘要失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建摘要生成的提示词
     *
     * @param fileContent 文件内容
     * @return 提示词
     */
    private String buildSummaryPrompt(final String fileContent) {
        // 限制文件内容长度，避免超过大模型token限制
        String content = fileContent;
        if (content.length() > 8000) {
            content = content.substring(0, 8000) + "...";
        }

        return "请为以下报告内容生成一段不超过50字的摘要，要求简洁明了，突出核心内容和主要观点：\n\n" + content;
    }

    /**
     * 调用智谱AI同步接口
     *
     * @param prompt 提示词
     * @return AI响应内容
     */
    private String zhipuSyncCall(final String prompt) {
        try {
            // 构建请求体
            final JSONObject requestBody = new JSONObject();
            requestBody.put("model", "glm-4-flash-250414");

            final JSONObject message = new JSONObject();
            message.put("role", "user");
            message.put("content", prompt);

            final JSONObject[] messages = {message};
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", 100); // 限制返回长度
            requestBody.put("temperature", 0.7);

            // 发送请求，使用现有的配置
            final HttpResponse response = HttpRequest.post(modelParamConfig.getOneApiUrl())
                    .header("Authorization", modelParamConfig.getDpAccessToken())
                    .header("Content-Type", "application/json")
                    .body(requestBody.toJSONString())
                    .timeout(30000)
                    .execute();

            if (response.getStatus() == 200) {
                final JSONObject responseJson = JSON.parseObject(response.body());
                final JSONObject choices = responseJson.getJSONArray("choices").getJSONObject(0);
                final JSONObject messageObj = choices.getJSONObject("message");
                return messageObj.getString("content").trim();
            } else {
                log.error("智谱AI调用失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                return null;
            }

        } catch (final Exception e) {
            log.error("调用智谱AI异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名
     */
    private String getFileExtension(final String fileName) {
        if (CharSequenceUtil.isBlank(fileName)) {
            return ".tmp";
        }
        final int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : ".tmp";
    }

    /**
     * 解析PDF文件内容
     *
     * @param file PDF文件
     * @return 文件内容
     */
    private String readPdfFile(final File file) {
        try {
            // 加载PDF文件
            final PDDocument document = PDDocument.load(file);
            // 检查文档是否加密
            if (document.isEncrypted()) {
                log.error("PDF document is encrypted, can't read content.");
                document.close();
                return null;
            }
            // 使用PDFTextStripper来提取文本
            final PDFTextStripper pdfStripper = new PDFTextStripper();
            // 设置提取的页面范围（限制前100页避免内容过长）
            pdfStripper.setStartPage(1);
            pdfStripper.setEndPage(Math.min(100, document.getNumberOfPages()));
            final String text = pdfStripper.getText(document);
            // 关闭文档
            document.close();
            return text;
        } catch (final IOException e) {
            log.error("Error reading PDF file: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析Word文档内容
     *
     * @param file Word文件
     * @return 文件内容
     * @throws IOException IO异常
     */
    private String parseWordDocument(final File file) throws IOException {
        final String fileName = file.getName().toLowerCase();

        try (final FileInputStream fis = new FileInputStream(file)) {
            if (fileName.endsWith(".docx")) {
                return parseDocx(fis);
            } else if (fileName.endsWith(".doc")) {
                return parseDoc(fis);
            } else {
                throw new IllegalArgumentException("Unsupported file type: " + fileName);
            }
        }
    }

    /**
     * 解析DOCX文档内容
     *
     * @param fis 文件输入流
     * @return 文档内容
     * @throws IOException IO异常
     */
    private String parseDocx(final FileInputStream fis) throws IOException {
        try (final XWPFDocument document = new XWPFDocument(fis)) {
            final StringBuilder content = new StringBuilder();

            // 提取所有段落文本
            for (final XWPFParagraph paragraph : document.getParagraphs()) {
                final String text = paragraph.getText().trim();
                if (CharSequenceUtil.isNotBlank(text)) {
                    content.append(text).append("\n");
                }
            }

            return content.toString();
        }
    }

    /**
     * 解析DOC文档内容
     *
     * @param fis 文件输入流
     * @return 文档内容
     * @throws IOException IO异常
     */
    private String parseDoc(final FileInputStream fis) throws IOException {
        try (final HWPFDocument doc = new HWPFDocument(fis)) {
            final Range range = doc.getRange();
            return range.text();
        }
    }

}
