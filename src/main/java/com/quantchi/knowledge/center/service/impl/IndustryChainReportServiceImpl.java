package com.quantchi.knowledge.center.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
// 移除未使用的OSS相关导入，使用通用的文件存储服务
import com.quantchi.knowledge.center.bean.dto.IndustryChainReportContentDTO;
import com.quantchi.knowledge.center.bean.dto.IndustryChainReportDTO;
import com.quantchi.knowledge.center.bean.entity.DmDivision;
import com.quantchi.knowledge.center.bean.entity.Knowledge;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.model.ReportGenerateQuery;
import com.quantchi.knowledge.center.bean.vo.IndustryChainReportVO;
import com.quantchi.knowledge.center.service.IDmDivisionService;
import com.quantchi.knowledge.center.service.IFileStorageService;
import com.quantchi.knowledge.center.service.IIndustryChainService;
import com.quantchi.knowledge.center.service.ISysFileService;
import com.quantchi.knowledge.center.service.IndustryChainReportService;
import com.quantchi.knowledge.center.util.HttpCallClient;
import com.quantchi.knowledge.center.util.LocalFileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.UUID;

/**
 * 产业链报告服务实现类
 *
 * <AUTHOR>
 * @date 2025/03/19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class IndustryChainReportServiceImpl implements IndustryChainReportService {

    private final IIndustryChainService industryChainService;
    private final ISysFileService sysFileService;
    private final IFileStorageService fileStorageService;
    private final IDmDivisionService dmDivisionService;

    @Value("${oss.profiles.active}")
    private String env;

    @Value("${file.preUrl}")
    private String filePreUrl;

    @Value("${file.path}")
    private String filePath;

    @Value("${report.url}")
    private String reportGenerateUrl;

    @Value("${aliyun.oss.bucketName}")
    private String bucketName;
    
    // 缓存最近生成的报告文件，避免重复生成
    // key: 缓存键(chainId_title_keywords), value: 报告文件信息(fileKey,fileUrl,tempFile)
    private final Map<String, ReportFileInfo> reportFileCache = new HashMap<>();
    
    /**
     * 报告文件信息类，用于缓存
     */
    private static class ReportFileInfo {
        private String fileKey;
        private String fileUrl;
        private File tempFile;
        private List<IndustryChainReportVO.TocEntryVO> tocEntries;
        private boolean contentProcessed = false;
        
        public ReportFileInfo(String fileKey, String fileUrl, File tempFile) {
            this.fileKey = fileKey;
            this.fileUrl = fileUrl;
            this.tempFile = tempFile;
        }
        
        public void markContentProcessed() {
            this.contentProcessed = true;
        }
        
        public boolean isContentProcessed() {
            return contentProcessed;
        }
    }
    
    /**
     * 获取产业链报告目录
     * @return 报告目录
     */
    @Override
    public List<IndustryChainReportVO.TocEntryVO> getIndustryChainReportToc(final IndustryChainReportDTO reportDTO) {
        final String chainId = reportDTO.getChainId();
        final String companyId = reportDTO.getCompanyId();
        final String regionId = reportDTO.getRegionId();
        final Integer type = reportDTO.getType();
        if (Objects.equals(type, 1) && CharSequenceUtil.isBlank(chainId)) {
            throw new BusinessException("产业链ID不能为空");
        }
        if (Objects.equals(type, 2) && CharSequenceUtil.isBlank(regionId)) {
            throw new BusinessException("城市ID不能为空");
        }
        if (Objects.equals(type, 3) && CharSequenceUtil.isBlank(companyId)) {
            throw new BusinessException("企业ID不能为空");
        }

        // 生成缓存键
        String cacheKey = generateCacheKey(reportDTO);
        
        // 检查缓存中是否已有报告文件
        ReportFileInfo reportFileInfo = reportFileCache.get(cacheKey);
        
        if (reportFileInfo != null && reportFileInfo.tocEntries != null) {
            // 如果缓存中已有目录数据，直接返回
            log.info("从缓存中获取报告目录，缓存键: {}", cacheKey);
            return reportFileInfo.tocEntries;
        }

        // 构建查询参数
        ReportGenerateQuery query = buildReportQuery(reportDTO);

        // 获取报告文件
        String fileKey = getReportFileKey(query);
        if (CharSequenceUtil.isBlank(fileKey)) {
            throw new BusinessException("获取报告失败");
        }

        // 构建文件URL
        String instanceName = query.getInstance_name();
        String fileUrl = filePreUrl + filePath + "?fileName=" + fileKey + "&originFileName=" + instanceName + "." + fileKey.split("\\.")[1];

        try {
            // 创建临时文件
            String fileName = instanceName + "." + fileKey.split("\\.")[1];
            File tempFile = File.createTempFile("report_", "." + LocalFileUtil.getFileExtension(fileName));
            
            // 下载文件
            HttpUtil.downloadFile(fileUrl, tempFile);
            
            // 提取目录
            List<IndustryChainReportVO.TocEntryVO> tocEntries = extractTocEntries(tempFile);
            
            // 将文件信息保存到缓存中
            if (reportFileInfo == null) {
                reportFileInfo = new ReportFileInfo(fileKey, fileUrl, tempFile);
                reportFileCache.put(cacheKey, reportFileInfo);
            } else {
                // 如果已经有缓存对象，更新它
                reportFileInfo.fileKey = fileKey;
                reportFileInfo.fileUrl = fileUrl;
                // 如果已有临时文件，先删除它
                if (reportFileInfo.tempFile != null && reportFileInfo.tempFile.exists() && !reportFileInfo.tempFile.equals(tempFile)) {
                    reportFileInfo.tempFile.delete();
                }
                reportFileInfo.tempFile = tempFile;
            }
            reportFileInfo.tocEntries = tocEntries;
            
            return tocEntries;
        } catch (Exception e) {
            log.error("下载或解析报告失败", e);
            throw new BusinessException("获取报告目录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取产业链报告内容（仅Markdown内容）
     * @return 报告Markdown内容
     */
    @Override
    public String getIndustryChainReportContent(final IndustryChainReportContentDTO reportContentDTO) {
        final String chainId = reportContentDTO.getChainId();
        if (CharSequenceUtil.isBlank(chainId)) {
            throw new BusinessException("产业链ID不能为空");
        }
        
        // 生成缓存键
        final IndustryChainReportDTO reportDTO = new IndustryChainReportDTO();
        BeanUtils.copyProperties(reportContentDTO, reportDTO);
        String cacheKey = generateCacheKey(reportDTO);
        
        // 检查缓存中是否已有报告文件
        ReportFileInfo reportFileInfo = reportFileCache.get(cacheKey);
        
        // 如果缓存中已有报告文件且文件存在，直接解析内容
        if (reportFileInfo != null && reportFileInfo.tempFile != null && reportFileInfo.tempFile.exists()) {
            log.info("从缓存中获取报告文件，缓存键: {}", cacheKey);
            try {
                // 直接解析文件内容，不需要重新生成和下载
                String content = analysisFileContent(reportFileInfo.tempFile);
                // 标记该文件内容已被处理
                reportFileInfo.markContentProcessed();
                
                // 清理已处理过内容的缓存
                cleanupProcessedCache();
                
                return content;
            } catch (Exception e) {
                log.error("从缓存中解析报告失败", e);
                // 如果从缓存解析失败，删除缓存并重新生成
                reportFileCache.remove(cacheKey);
            }
        }

        // 构建查询参数
        ReportGenerateQuery query = buildReportQuery(reportDTO);
        
        // 获取报告文件，并检查是否为当天生成的
        String fileKey = getReportFileKey(query, true);
        if (CharSequenceUtil.isBlank(fileKey)) {
            throw new BusinessException("获取报告失败");
        }
        
        // 构建文件URL
        String instanceName = query.getInstance_name();
        String fileUrl = filePreUrl + filePath + "?fileName=" + fileKey + "&originFileName=" + instanceName + "." + fileKey.split("\\.")[1];

        try {
            // 创建临时文件
            String fileName = instanceName + "." + fileKey.split("\\.")[1];
            File tempFile = File.createTempFile("report_", "." + LocalFileUtil.getFileExtension(fileName));
            
            // 下载文件
            HttpUtil.downloadFile(fileUrl, tempFile);
            
            // 解析文件内容
            String content = analysisFileContent(tempFile);
            
            // 将文件信息保存到缓存中
            if (reportFileInfo == null) {
                reportFileInfo = new ReportFileInfo(fileKey, fileUrl, tempFile);
                reportFileCache.put(cacheKey, reportFileInfo);
            } else {
                // 如果已经有缓存对象，更新它
                reportFileInfo.fileKey = fileKey;
                reportFileInfo.fileUrl = fileUrl;
                // 如果已有临时文件，先删除它
                if (reportFileInfo.tempFile != null && reportFileInfo.tempFile.exists() && !reportFileInfo.tempFile.equals(tempFile)) {
                    reportFileInfo.tempFile.delete();
                }
                reportFileInfo.tempFile = tempFile;
            }
            
            // 标记该文件内容已被处理
            reportFileInfo.markContentProcessed();
            
            // 清理已处理过内容的缓存
            cleanupProcessedCache();
            
            return content;
        } catch (Exception e) {
            log.error("下载或解析报告失败", e);
            throw new BusinessException("获取报告内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取报告文件的Key
     *
     * @param query 查询参数
     * @return 文件Key
     */
    private String getReportFileKey(ReportGenerateQuery query) {
        return getReportFileKey(query, true);
    }
    
    /**
     * 获取报告文件的Key
     *
     * @param query 查询参数
     * @param checkDate 是否检查文件日期（当天生成的文件不需要重新生成）
     * @return 文件Key
     */
    private String getReportFileKey(ReportGenerateQuery query, boolean checkDate) {
        String instanceType = query.getInstance_type();
        String instanceId = query.getInstance_id();
        
        // 默认文件Key
        String fileKey = env + "/" + instanceId + "_" + instanceType + "_report.docx";
        
        File tempFile = null;
        try {
            // 使用文件存储服务检查文件是否存在
            // 增加UUID提高临时文件名随机性，避免并发冲突
            tempFile = File.createTempFile("report_" + UUID.randomUUID().toString(), "_check");

            try {
                fileStorageService.downloadObject(bucketName, fileKey, tempFile);
                
                // 获取文件上次修改时间
                final Date lastModified = new Date(tempFile.lastModified());
                
                // 如果需要检查日期且文件不是当天的，重新生成
                if (checkDate && lastModified.before(DateUtil.beginOfDay(new Date()))) {
                    log.info("文件不是当天生成的，重新生成文件: {}", fileKey);
                    fileKey = generateReportFile(query);
                } else {
                    log.info("使用已存在的文件: {}, 上次修改时间: {}", fileKey, DateUtil.formatDateTime(lastModified));
                }
            } catch (Exception e) {
                log.info("文件不存在，将生成新文件: {}", fileKey);
                fileKey = generateReportFile(query);
            }
        } catch (Exception e) {
            log.error("检查文件失败，生成新文件", e);
            fileKey = generateReportFile(query);
        } finally {
            // 确保临时文件被删除
            if (tempFile != null && tempFile.exists()) {
                if (!tempFile.delete()) {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                    tempFile.deleteOnExit();
                }
            }
        }
        
        return fileKey;
    }
    
    /**
     * 调用外部接口生成报告文件
     *
     * @param query 查询参数
     * @return 文件Key
     */
    private String generateReportFile(ReportGenerateQuery query) {
        log.info("调用外部接口生成报告文件: {}", JSON.toJSONString(query));
        return HttpCallClient.getStringDataFromUrl(reportGenerateUrl, JSON.toJSONString(query));
    }

    /**
     * 下载并解析报告
     *
     * @param fileUrl      文件URL
     * @param instanceName 实例名称
     * @param fileKey      文件Key
     * @return 报告内容和目录
     */
    private IndustryChainReportVO downloadAndParseReport(String fileUrl, String instanceName, String fileKey) {
        IndustryChainReportVO reportVO = new IndustryChainReportVO();
        
        try {
            // 创建临时文件
            String fileName = instanceName + "." + fileKey.split("\\.")[1];
            File tempFile = File.createTempFile("report_", "." + LocalFileUtil.getFileExtension(fileName));
            
            // 下载文件
            HttpUtil.downloadFile(fileUrl, tempFile);
            
            // 解析文件内容
            String content = analysisFileContent(tempFile);
            reportVO.setContent(content);
            
            // 提取目录
            List<IndustryChainReportVO.TocEntryVO> tocEntries = extractTocEntries(tempFile);
            reportVO.setTocEntries(tocEntries);
            
            // 删除临时文件
            tempFile.delete();
            
            return reportVO;
        } catch (Exception e) {
            log.error("下载或解析报告失败", e);
            throw new BusinessException("获取报告内容失败: " + e.getMessage());
        }
    }

    /**
     * 解析文件内容
     *
     * @param file 文件
     * @return 文件内容
     * @throws IOException IO异常
     */
    private String analysisFileContent(File file) throws IOException {
        return sysFileService.analysisFileContent(file);
    }
    
    /**
     * 生成缓存键
     *
     * @param reportDTO 报告请求DTO
     * @return 缓存键
     */
    private String generateCacheKey(final IndustryChainReportDTO reportDTO) {
        final Integer type = reportDTO.getType();
        final String title = reportDTO.getTitle();
        final List<String> keywords = reportDTO.getKeywords();
        
        StringBuilder sb = new StringBuilder();
        
        // 根据报告类型构建缓存键的前缀
        if (Objects.equals(type, 1)) {
            // 产业链分析洞察报告
            sb.append("industry_").append(reportDTO.getChainId());
        } else if (Objects.equals(type, 2)) {
            // 城市招商报告
            sb.append("courtship_").append(reportDTO.getChainId()).append("_").append(reportDTO.getRegionId());
        } else if (Objects.equals(type, 3)) {
            // 招商项目评估报告
            sb.append("company_").append(reportDTO.getCompanyId());
        }
        
        // 添加标题和关键词
        if (CharSequenceUtil.isNotBlank(title)) {
            sb.append("_").append(title);
        }
        if (keywords != null && !keywords.isEmpty()) {
            sb.append("_").append(String.join(",", keywords));
        }
        
        return sb.toString();
    }
    


    /**
     * 在应用关闭时清理所有临时文件
     */
    @PreDestroy
    public void cleanupTempFiles() {
        log.info("应用关闭，清理所有临时文件...");
        for (ReportFileInfo fileInfo : reportFileCache.values()) {
            if (fileInfo.tempFile != null && fileInfo.tempFile.exists()) {
                try {
                    boolean deleted = fileInfo.tempFile.delete();
                    if (deleted) {
                        log.info("成功删除临时文件: {}", fileInfo.tempFile.getAbsolutePath());
                    } else {
                        log.warn("无法删除临时文件: {}", fileInfo.tempFile.getAbsolutePath());
                    }
                } catch (Exception e) {
                    log.error("删除临时文件时发生错误: {}", fileInfo.tempFile.getAbsolutePath(), e);
                }
            }
        }
        reportFileCache.clear();
    }
    
    /**
     * 构建报告查询参数
     *
     * @param reportDTO 报告请求DTO
     * @return 查询参数
     */
    private ReportGenerateQuery buildReportQuery(final IndustryChainReportDTO reportDTO) {
        final Integer type = reportDTO.getType();
        final String title = reportDTO.getTitle();
        final List<String> keywords = reportDTO.getKeywords();
        final ReportGenerateQuery query = new ReportGenerateQuery();
        
        if (Objects.equals(type, 1)) {
            // 产业链分析洞察报告
            final String chainId = reportDTO.getChainId();
            final Knowledge knowledge = industryChainService.getById(chainId);
            if (knowledge == null) {
                throw new BusinessException("产业链不存在");
            }
            query.setInstance_id(knowledge.getId());
            query.setInstance_name(knowledge.getName());
            query.setInstance_type("industry");
        } else if (Objects.equals(type, 2)) {
            // 城市招商报告
            final String regionId = reportDTO.getRegionId();
            final String chainId = reportDTO.getChainId();
            final Knowledge knowledge = industryChainService.getById(chainId);
            if (knowledge == null) {
                // todo 暂时只设置视觉智能链
                query.setInstance_id("industry_cv");
                query.setInstance_name("视觉智能");
            } else {
                query.setInstance_id(knowledge.getId());
                query.setInstance_name(knowledge.getName());
            }
            query.setInstance_type("industry_courtship");
            final DmDivision division = dmDivisionService.getById(regionId);
            query.setCity(division.getName()); // 设置城市参数
        } else if (Objects.equals(type, 3)) {
            // todo 招商项目评估报告
            final String companyId = reportDTO.getCompanyId();
            query.setInstance_id(companyId);
            query.setInstance_name("企业评估"); // 可能需要从其他服务获取企业名称
            query.setInstance_type("company");
        }

        if (CharSequenceUtil.isNotBlank(title)) {
            query.setTitle(title);
        }
        if (keywords != null && !keywords.isEmpty()) {
            query.setKeywords(keywords);
        }
        
        return query;
    }
    
    /**
     * 清理已处理过内容的缓存项
     */
    private void cleanupProcessedCache() {
        List<String> keysToRemove = new ArrayList<>();
        
        // 找出所有已处理过内容的缓存项
        for (Map.Entry<String, ReportFileInfo> entry : reportFileCache.entrySet()) {
            if (entry.getValue().isContentProcessed()) {
                keysToRemove.add(entry.getKey());
            }
        }
        
        // 删除这些缓存项并清理临时文件
        for (String key : keysToRemove) {
            ReportFileInfo fileInfo = reportFileCache.get(key);
            if (fileInfo != null && fileInfo.tempFile != null && fileInfo.tempFile.exists()) {
                try {
                    boolean deleted = fileInfo.tempFile.delete();
                    if (deleted) {
                        log.info("成功删除已处理的临时文件: {}", fileInfo.tempFile.getAbsolutePath());
                    } else {
                        log.warn("无法删除已处理的临时文件: {}", fileInfo.tempFile.getAbsolutePath());
                    }
                } catch (Exception e) {
                    log.error("删除已处理的临时文件时发生错误: {}", fileInfo.tempFile.getAbsolutePath(), e);
                }
            }
            reportFileCache.remove(key);
            log.info("已从缓存中移除处理完毕的报告: {}", key);
        }
    }
    
    /**
     * 提取文档中的目录项
     *
     * @param file 文件
     * @return 目录项列表
     */
    private List<IndustryChainReportVO.TocEntryVO> extractTocEntries(File file) {
        List<IndustryChainReportVO.TocEntryVO> tocEntries = new ArrayList<>();
        
        try {
            String fileName = file.getName().toLowerCase();
            if (fileName.endsWith(".docx")) {
                try (FileInputStream fis = new FileInputStream(file);
                     XWPFDocument document = new XWPFDocument(fis)) {
                    
                    // 处理段落中的标题
                    for (XWPFParagraph paragraph : document.getParagraphs()) {
                        // 检查段落样式是否为标题
                        String styleName = paragraph.getStyle();
                        if (styleName != null && styleName.startsWith("Heading")) {
                            try {
                                int level = Integer.parseInt(styleName.substring("Heading".length()).trim());
                                String title = paragraph.getText();
                                
                                IndustryChainReportVO.TocEntryVO entry = new IndustryChainReportVO.TocEntryVO();
                                entry.setTitle(title);
                                entry.setLevel(level);
                                
                                // 处理标题文本，生成锚点ID
                                String anchorId = title.toLowerCase()
                                        .replaceAll("[^\\w\\s-]", "") // 移除特殊字符
                                        .replaceAll("\\s+", "-"); // 空格替换为连字符
                                entry.setAnchorId(anchorId);
                                
                                tocEntries.add(entry);
                            } catch (NumberFormatException e) {
                                // 忽略非标准标题样式
                            }
                        }
                        
                        // 检查是否是中文数字标题格式（如：一、二、三、等）
                        String text = paragraph.getText().trim();
                        if (text.matches("^[一二三四五六七八九十]+、.*")) {
                            // 提取中文数字部分作为标题级别
                            String prefix = text.substring(0, text.indexOf("、") + 1);
                            String titleText = text.substring(text.indexOf("、") + 1).trim();
                            
                            // 根据前缀长度确定标题级别
                            int chineseLevel = 2; // 默认为二级标题
                            
                            IndustryChainReportVO.TocEntryVO entry = new IndustryChainReportVO.TocEntryVO();
                            entry.setTitle(prefix + " " + titleText);
                            entry.setLevel(chineseLevel);
                            
                            // 处理标题文本，生成锚点ID
                            String anchorId = titleText.toLowerCase()
                                    .replaceAll("[^\\w\\s-]", "") // 移除特殊字符
                                    .replaceAll("\\s+", "-"); // 空格替换为连字符
                            entry.setAnchorId(anchorId);
                            
                            tocEntries.add(entry);
                        }
                    }
                }
            } else if (fileName.endsWith(".doc")) {
                // 对于.doc文件，可以添加相应的处理逻辑
                // 由于复杂性，这里简化处理
                log.warn("目前不支持.doc文件的目录提取，返回空目录");
            } else if (fileName.endsWith(".pdf")) {
                // 对于PDF文件，可以添加相应的处理逻辑
                // 由于复杂性，这里简化处理
                log.warn("目前不支持PDF文件的目录提取，返回空目录");
            }
        } catch (Exception e) {
            log.error("提取目录失败", e);
        }
        
        return tocEntries;
    }
}
