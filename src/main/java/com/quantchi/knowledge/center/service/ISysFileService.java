package com.quantchi.knowledge.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.knowledge.center.bean.entity.SysFile;
import com.quantchi.knowledge.center.bean.vo.SysFileVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;

/**
 * <p>
 * 文件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
public interface ISysFileService extends IService<SysFile> {

    SysFileVO uploadBlobFile(MultipartFile file);

    SysFileVO dealWithFile(String originalFilename, String fileName, String fileType, MultipartFile file) throws Exception;

    SysFileVO uploadFile(final MultipartFile file, final String fileType) throws Exception;

    SysFileVO uploadFile(File file, String fileName, final String suffix) throws Exception;

    SysFileVO uploadFileWithPreview(File file, String fileName, String suffix, String fileType) throws Exception;

    String saveTempFileToOSS(File file, String storageFileName) throws IOException;

    void downloadFile(String fileName,
                      String originFileName,
                      Boolean preview,
                      final Long reportId,
                      final String token,
                      HttpServletResponse response);

    InputStream getInputStreamFromLocal(String fileName) throws FileNotFoundException;

    boolean deleteFile(String fileName);

    String analysisFileContent(MultipartFile file) throws IOException;

    String analysisFileContent(File file) throws IOException;

    /**
     * 通过fileKey保存文件到数据库
     *
     * @param fileKey 文件key（OSS中的文件名）
     * @param originalFileName 原始文件名
     * @return 文件ID
     * @throws Exception 异常
     */
    Long saveFileByKey(String fileKey, String originalFileName) throws Exception;

    /**
     * 通过文件ID获取文件对象
     *
     * @param fileId 文件ID
     * @return 文件对象
     * @throws Exception 异常
     */
    File getFileById(Long fileId) throws Exception;
}
