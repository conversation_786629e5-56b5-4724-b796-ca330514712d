package com.quantchi.knowledge.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.knowledge.center.bean.entity.SysFile;
import com.quantchi.knowledge.center.bean.vo.SysFileVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;

/**
 * <p>
 * 文件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
public interface ISysFileService extends IService<SysFile> {

    SysFileVO uploadBlobFile(MultipartFile file);

    SysFileVO dealWithFile(String originalFilename, String fileName, String fileType, MultipartFile file) throws Exception;

    SysFileVO uploadFile(final MultipartFile file, final String fileType) throws Exception;

    SysFileVO uploadFile(File file, String fileName, final String suffix) throws Exception;

    String saveTempFileToOSS(File file, String storageFileName) throws IOException;

    void downloadFile(String fileName,
                      String originFileName,
                      Boolean preview,
                      final Long reportId,
                      final String token,
                      HttpServletResponse response);

    InputStream getInputStreamFromLocal(String fileName) throws FileNotFoundException;

    boolean deleteFile(String fileName);

    String analysisFileContent(MultipartFile file) throws IOException;

    String analysisFileContent(File file) throws IOException;
}
