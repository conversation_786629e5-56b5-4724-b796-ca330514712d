package com.quantchi.knowledge.center.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.quantchi.knowledge.center.service.IFileStorageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.knowledge.center.bean.entity.SysFile;
import com.quantchi.knowledge.center.bean.entity.SysReport;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.vo.SysFileVO;
import com.quantchi.knowledge.center.dao.mysql.SysFileMapper;
import com.quantchi.knowledge.center.dao.mysql.SysReportMapper;
import com.quantchi.knowledge.center.service.ISysFileService;
import com.quantchi.knowledge.center.service.ISysUserDownloadService;
import com.quantchi.knowledge.center.util.LocalFileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.usermodel.*;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBookmark;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.*;

import static com.quantchi.knowledge.center.controller.SysFileController.*;
import static com.quantchi.knowledge.center.util.LocalFileUtil.getActualFilePath;
import static com.quantchi.knowledge.center.util.LocalFileUtil.getFileSize;

/**
 * <p>
 * 文件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysFileServiceImpl extends ServiceImpl<SysFileMapper, SysFile> implements ISysFileService {

    @Value("${file.preUrl}")
    private String filePreUrl;

    @Value("${file.path}")
    private String filePath;

    @Value("${file.save.choose}")
    private Integer fileSaveChoose;

    @Autowired
    private IFileStorageService fileStorageService;

    private final SysReportMapper sysReportMapper;
    private final ISysUserDownloadService sysUserDownloadService;
    private final ThreadPoolTaskExecutor docExecutor;

    /**
     * 读取oss文件流，注意，需要使用字节流读取
     *
     * @param inStream
     * @return
     * @throws IOException
     */
    public static byte[] readOSSFileStream(final InputStream inStream) throws IOException {
        final ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        final byte[] buffer = new byte[1024];
        int len = -1;
        while ((len = inStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, len);
        }
        outStream.close();
        inStream.close();
        return outStream.toByteArray();
    }

    @Override
    public SysFileVO uploadBlobFile(final MultipartFile file) {
        try {
            // 将MultipartFile转换为BufferedImage
            final InputStream inputStream = file.getInputStream();
            final BufferedImage image = ImageIO.read(inputStream);
            if (image == null) {
                throw new BusinessException("文件上传失败");
            }
            // 将BufferedImage转换为PNG格式的字节数组
            final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", outputStream);
            final byte[] pngBytes = outputStream.toByteArray();
            final String oriName = file.getOriginalFilename();
            return uploadFileForBlob(pngBytes, oriName, "png");
        } catch (final Exception e) {
            log.error("素材上传失败", e);
            throw new BusinessException("文件上传失败");
        }
    }

    public SysFileVO uploadFileForBlob(final byte[] pngBytes, final String oriName, final String suffix) {
        final ObjectMetadata meta = new ObjectMetadata();
        meta.setContentType("image/png");
        final String storageFileName = LocalFileUtil.getStorageFileName(FILE_MODULE_NAME, suffix);
        fileStorageService.putObject(fileStorageService.getDefaultBucketName(), storageFileName, new ByteArrayInputStream(pngBytes), "image/png");

        final SysFile sysFile = new SysFile();
        sysFile.setFileName(storageFileName);
        final String url = filePreUrl + filePath + "?fileName=" + storageFileName + "&originFileName=" + oriName + ".png";
        sysFile.setUrl(url);
        sysFile.setOriName(oriName + suffix);
        sysFile.setModule(FILE_MODULE_NAME);
        // 保存文件相关信息到数据库中
        this.save(sysFile);
        final SysFileVO vo = new SysFileVO();
        BeanUtils.copyProperties(sysFile, vo);
        vo.setFileId(sysFile.getFileId());

        return vo;
    }

    private String singleUploadFile(final MultipartFile file, final String fileType) throws Exception {
        String fileName = "";
        if (file == null) {
            throw new BusinessException("上传文件不可为空！");
        }
        final String originalFilename = file.getOriginalFilename();
        if (!file.isEmpty()) {
            // 检查文件大小是否超过100mb 1024 * 1024 * 100
            if (file.getSize() > 104857600) {
                throw new BusinessException("文件大小上限：100M！");
            }
            if (Objects.equals(fileType, "report")) {
                // 检查文件类型是否为pdf、doc、docx、pptx
                if (!StrUtil.endWithAnyIgnoreCase(originalFilename, "pdf", "doc", "docx", "pptx")) {
                    throw new BusinessException("上传文件类型错误，请上传pdf、doc、docx、pptx格式的文件！");
                }
            }
            if (Objects.equals(fileSaveChoose, 0)) {
                fileName = saveFileToOSS(file, "");
            } else if (Objects.equals(fileSaveChoose, 1)) {
                fileName = saveFileToLocal(file);
            }
        }
        return fileName;
    }

    /**
     * 文件后续处理，写入sys_file表，报告的话需要加入预览队列
     */
    @Override
    public SysFileVO dealWithFile(final String originalFilename, final String fileName, final String fileType, final MultipartFile file) throws Exception {
        final SysFileVO vo = new SysFileVO();
        final SysFile sysFile = new SysFile();
        sysFile.setFileName(fileName);
        final String url = filePreUrl + filePath + "?fileName=" + fileName + "&originFileName=" + originalFilename;
        sysFile.setUrl(url);
        sysFile.setOriName(originalFilename);
        sysFile.setModule(FILE_MODULE_NAME);
        sysFile.setFileType(fileType);
        sysFile.setCreateTime(new Date());
        sysFile.setUpdateTime(new Date());
        if (file != null) {
            final long size = file.getSize();
            sysFile.setFileSize(size);
            vo.setFileSize(getFileSize(size));
        }

        // 如果是文档类型的文件，解析并存储文件内容
        if (file != null && Objects.equals(fileType, CHAT_DOC_TYPE)
                && (originalFilename.toLowerCase().endsWith(".doc") ||
                originalFilename.toLowerCase().endsWith(".docx") ||
                originalFilename.toLowerCase().endsWith(".pdf"))) {
            try {
                String fileContent = analysisFileContent(file);
                sysFile.setFileContent(fileContent);
            } catch (Exception e) {
                log.error("解析文件内容失败", e);
            }
        }

        File fileLocal;
        if (file == null) {
            // 由前端预先上传文件，那么需要先下载文件到本地
            final String actualFilePath = getActualFilePath("temp", fileName);
            fileLocal = new File(actualFilePath);
            fileStorageService.downloadObject(fileStorageService.getDefaultBucketName(), fileName, fileLocal);
            log.info("下载文件成功，文件大小为{}", fileLocal.length());
        } else {
            try {
                fileLocal = LocalFileUtil.saveFileLocal(file, FILE_MODULE_NAME, fileName);
                sysFile.setMd5(DigestUtils.md5DigestAsHex(file.getInputStream()));
            } catch (Exception e) {
                log.error("计算md5失败", e);
                fileLocal = null;
            }
        }
        // 保存文件相关信息到数据库中
        this.save(sysFile);
        BeanUtils.copyProperties(sysFile, vo);
        vo.setFileId(sysFile.getFileId());
        if (Objects.equals(fileType, REPORT_TYPE) && fileLocal != null) {
            vo.setPreviewUrl(url + "&preview=true");
            // 判断文件类型，如果是pdf那么不需要通过预览服务器，但是需要通过pdf box解析获得首页图片
            final String suffix = FileNameUtil.getSuffix(originalFilename);
            if (Objects.equals(suffix, "pdf")) {
                final File imageFile = convertFirstPageToImage(fileLocal, getActualFilePath(FILE_MODULE_NAME, fileName.replace("." + suffix, ".png")));
                // 上传生成完的图片到oss中
                if (imageFile != null) {
                    final String imageFileName = imageFile.getName();
                    fileStorageService.putObject(fileStorageService.getDefaultBucketName(), imageFileName, Files.newInputStream(imageFile.toPath()), null);
                    vo.setHomePageUrl(filePreUrl + filePath + "?fileName=" + imageFileName + "&originFileName=" + imageFileName);
                }
            } else {
                final String previewServerUrl = filePreUrl + "/preview";
                // 设置文件预览链接
                final HashMap<String, Object> paramMap = new HashMap<>();
                //文件上传只需将参数中的键指定（默认file），值设为文件对象即可，对于使用者来说，文件上传与普通表单提交并无区别
                paramMap.put("file", fileLocal);
                final String filePreviewUrl = previewServerUrl + "/demo/" + fileLocal.getName();
                vo.setPreviewUrl(previewServerUrl + "/onlinePreview?url="
                        + Base64Utils.encodeToUrlSafeString(filePreviewUrl.getBytes()));
                docExecutor.execute(() -> {
                    String result = HttpRequest.post(previewServerUrl + "/fileUpload")
                            .header(Header.CONTENT_TYPE, "multipart/form-data")
                            .header("Accept", "*/*")
                            .header("Connection", "keep-alive")
                            .form(paramMap)
                            .timeout(20000)//超时，毫秒
                            .execute().body();
                    log.info("上传预览文件: {}", result);
                    // 加入转码队列
                    result = HttpUtil.get(previewServerUrl + "/addTask?url=" + filePreviewUrl);
                    log.info("加入转码队列: {}", result);
                });
            }
        }
        return vo;
    }

    public static File convertFirstPageToImage(final File fileLocal, final String imagePath) {
        try (final PDDocument document = PDDocument.load(fileLocal)) {
            final PDFRenderer pdfRenderer = new PDFRenderer(document);
            // 渲染第一页，dpi=300
            final BufferedImage image = pdfRenderer.renderImageWithDPI(0, 300);
            // 保存为PNG格式
            ImageIO.write(image, "PNG", new File(imagePath));
            return new File(imagePath);
        } catch (IOException e) {
            log.error("文件转换失败", e);
            return null;
        }
    }

    @Override
    public SysFileVO uploadFile(final MultipartFile file, final String fileType) throws Exception {
        final String originalFilename = file.getOriginalFilename();
        final String fileName = singleUploadFile(file, fileType);
        return dealWithFile(originalFilename, fileName, fileType, file);
    }

    public static void main(String[] args) {
        final String url = "http://************:30899/demo/新一代人工智能.docx";
        System.out.println(Base64.encodeBase64String(url.getBytes()));
    }

    @Override
    public SysFileVO uploadFile(final File file, final String fileName, final String suffix) throws Exception {
        if (file == null) {
            throw new BusinessException("上传文件不可为空！");
        }
        final String storageFileName = LocalFileUtil.getStorageFileName(FILE_MODULE_NAME, suffix);
        fileStorageService.putObject(fileStorageService.getDefaultBucketName(), storageFileName, file);
        final SysFile sysFile = new SysFile();
        sysFile.setFileName(storageFileName);
        final String url = filePreUrl + filePath + "?fileName=" + storageFileName + "&originFileName=" + fileName;
        sysFile.setUrl(url);
        sysFile.setOriName(fileName);
        sysFile.setModule(FILE_MODULE_NAME);
        // 保存文件相关信息到数据库中
        this.save(sysFile);
        final SysFileVO vo = new SysFileVO();
        BeanUtils.copyProperties(sysFile, vo);
        vo.setFileId(sysFile.getFileId());
        file.delete();
        return vo;
    }

    private String saveFileToOSS(final MultipartFile file, String storageFileName) throws IOException {
        final String suffix = FileNameUtil.getSuffix(file.getOriginalFilename());
        if (StrUtil.isBlank(storageFileName)) {
            storageFileName = LocalFileUtil.getStorageFileName(FILE_MODULE_NAME, suffix);
        }
        fileStorageService.putObject(fileStorageService.getDefaultBucketName(), storageFileName, file.getInputStream(), file.getContentType());
        return storageFileName;
    }

    /**
     * 保存文件到oss上
     * @param file
     * @param storageFileName
     * @return
     * @throws IOException
     */
    @Override
    public String saveTempFileToOSS(final File file, String storageFileName) throws IOException {
        final String suffix = FileNameUtil.getSuffix(file.getName());
        if (StrUtil.isBlank(storageFileName)) {
            storageFileName = LocalFileUtil.getStorageFileName(FILE_MODULE_NAME, suffix);
        }
        fileStorageService.putObject(fileStorageService.getDefaultBucketName(), storageFileName, file);
        return storageFileName;
    }

    private String saveFileToLocal(final MultipartFile file) throws Exception {
        return LocalFileUtil.saveFile(file, FILE_MODULE_NAME);
    }

    @Override
    public void downloadFile(final String fileName,
                             final String originFileName,
                             final Boolean preview,
                             final Long reportId,
                             final String token,
                             final HttpServletResponse response) {
        if (reportId != null) {
            // 更新报告的下载次数
            final SysReport sysReport = sysReportMapper.selectById(reportId);
            if (sysReport != null) {
                sysReport.setDownloadTimes(sysReport.getDownloadTimes() + 1);
                sysReportMapper.updateById(sysReport);
                // 保存下载记录
                if (CharSequenceUtil.isNotBlank(token)) {
                    final Object loginId = StpUtil.getLoginIdByToken(token);
                    if (loginId instanceof String) {
                        final SysFileVO vo = new SysFileVO();
                        vo.setFileName(fileName);
                        vo.setUrl(filePreUrl + filePath + "?fileName=" + fileName + "&originFileName=" + originFileName);
                        sysUserDownloadService.saveDownloadRecord(vo, sysReport.getTitle(), Long.parseLong((String) loginId));
                    }
                }
            }
        }

        // 使用流式下载避免内存溢出
        downloadFileStream(fileName, originFileName, preview, response);
    }

    /**
     * 流式下载文件，避免大文件导致的内存溢出
     *
     * @param fileName 存储文件名
     * @param originFileName 原始文件名
     * @param preview 是否预览模式
     * @param response HTTP响应对象
     */
    private void downloadFileStream(final String fileName,
                                   final String originFileName,
                                   final Boolean preview,
                                   final HttpServletResponse response) {
        InputStream inputStream = null;
        OutputStream outputStream = null;
        OSSObject ossObject = null;

        try {
            // 设置响应头
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "*");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setHeader("Access-Control-Allow-Credentials", "true");

            // 使用更精确的文件类型判断方法
            final String contentType = getContentTypeByFileName(originFileName, preview);
            response.setContentType(contentType);

            // 设置文件名
            final String encodedFileName = URLEncoder.encode(originFileName, "UTF-8").replaceAll("\\+", "%20");
            if (preview != null && preview) {
                response.addHeader("Content-Disposition", String.format("inline; filename=\"%s\"; filename*=UTF-8''%s",
                        encodedFileName, encodedFileName));
            } else {
                response.addHeader("Content-Disposition", String.format("attachment; filename=\"%s\"; filename*=UTF-8''%s",
                        encodedFileName, encodedFileName));
            }

            // 获取输入流和文件大小
            long fileSize = -1;
            if (Objects.equals(fileSaveChoose, 0)) {
                // OSS存储
                try {
                    inputStream = fileStorageService.getObject(fileStorageService.getDefaultBucketName(), fileName);
                    if (inputStream == null) {
                        log.error("从OSS获取文件流失败，文件名: {}", fileName);
                        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                        return;
                    }
                    // OSS文件大小暂时无法获取，设置为-1
                    fileSize = -1;
                } catch (Exception e) {
                    log.error("从OSS获取文件失败，文件名: {}", fileName, e);
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    return;
                }
            } else {
                // 本地存储
                try {
                    inputStream = getInputStreamFromLocal(fileName);
                    if (inputStream == null) {
                        log.error("从本地获取文件流失败，文件名: {}", fileName);
                        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                        return;
                    }
                    // 获取本地文件大小
                    fileSize = getFileSizeFromLocal(fileName);
                } catch (Exception e) {
                    log.error("从本地获取文件失败，文件名: {}", fileName, e);
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    return;
                }
            }

            // 设置Content-Length头，帮助浏览器显示下载进度
            if (fileSize > 0) {
                response.setContentLengthLong(fileSize);
            }

            // 获取输出流
            outputStream = new BufferedOutputStream(response.getOutputStream());

            // 流式传输文件内容，使用较小的缓冲区避免内存溢出
            streamCopyWithBuffer(inputStream, outputStream);

        } catch (final Exception e) {
            log.error("下载文件异常，文件名: {}, 原始文件名: {}", fileName, originFileName, e);
            if (!response.isCommitted()) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        } finally {
            // 确保资源被正确关闭
            IoUtil.close(inputStream);
            IoUtil.close(outputStream);
            IoUtil.close(ossObject);
        }
    }

    /**
     * 流式复制数据，使用固定大小的缓冲区避免内存溢出
     *
     * @param inputStream 输入流
     * @param outputStream 输出流
     * @throws IOException IO异常
     */
    private void streamCopyWithBuffer(final InputStream inputStream, final OutputStream outputStream) throws IOException {
        // 使用8KB缓冲区，平衡内存使用和传输效率
        final byte[] buffer = new byte[8192];
        int bytesRead;
        long totalBytesRead = 0;

        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
            totalBytesRead += bytesRead;

            // 每传输1MB数据刷新一次，避免缓冲区积累过多数据
            if (totalBytesRead % (1024 * 1024) == 0) {
                outputStream.flush();
            }
        }
        // 最终刷新
        outputStream.flush();
    }



    /**
     * 获取本地文件大小
     *
     * @param fileName 文件名
     * @return 文件大小，获取失败返回-1
     */
    private long getFileSizeFromLocal(final String fileName) {
        try {
            final File file = new File(getActualFilePath(FILE_MODULE_NAME, fileName));
            if (file.exists() && file.isFile()) {
                return file.length();
            }
        } catch (Exception e) {
            log.debug("获取本地文件大小失败: {}", e.getMessage());
        }
        return -1;
    }

    @Override
    public InputStream getInputStreamFromLocal(final String fileName) throws FileNotFoundException {
        final File file = new File(getActualFilePath(FILE_MODULE_NAME, fileName));
        if (!file.exists()) {
            return null;
        }
        // 将文件写入输入流
        final FileInputStream fileInputStream = new FileInputStream(file);
        return new BufferedInputStream(fileInputStream);
    }

    @Override
    public boolean deleteFile(final String fileName) {
        if (Objects.equals(fileSaveChoose, 0)) {
            try {
                fileStorageService.removeObject(fileStorageService.getDefaultBucketName(), fileName);
            } catch (final Exception e) {
                log.error("OSS删除失败");
                return false;
            }
            return true;
        } else {
            final File file = new File(getActualFilePath(FILE_MODULE_NAME, fileName));
            return file.delete();
        }
    }


    /**
     * 解析pdf内容
     *
     * @param file
     * @return
     */
    public static String readPdfFile(final File file) {
        try {
            // 加载PDF文件
            final PDDocument document = PDDocument.load(file);
            // 检查文档是否加密
            if (document.isEncrypted()) {
                log.error("PDF document is encrypted, can't read content.");
                return null;
            }
            // 使用PDFTextStripper来提取文本
            final PDFTextStripper pdfStripper = new PDFTextStripper();
            // 设置提取的页面范围
            pdfStripper.setStartPage(1);
            pdfStripper.setEndPage(100);
            final String text = pdfStripper.getText(document);
            // 关闭文档
            document.close();
            return text;
        } catch (final IOException e) {
            log.error("Error reading PDF file: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public String analysisFileContent(final MultipartFile file) throws IOException {
        // 创建一个临时文件
        final File tempFile = new File(System.getProperty("user.dir")
                + "/file/"
                + file.getOriginalFilename());
        // 将MultipartFile转换成File对象
        try {
            file.transferTo(tempFile);
        } catch (final IOException e) {
            log.error("Failed to transfer MultipartFile to File", e);
            return null;
        }
        final String fileContent = analysisFileContent(tempFile);
        tempFile.delete();
        return fileContent;
    }

    @Override
    public String analysisFileContent(final File file) throws IOException {
        final String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".docx") || fileName.endsWith(".doc")) {
            return parseWordDocument(file);
        } else if (fileName.endsWith(".pdf")) {
            return readPdfFile(file);
        } else {
            throw new BusinessException("不支持的文件类型");
        }
    }

    /**
     * 解析 Word 文档
     */
    public String parseWordDocument(File file) throws IOException {
        String fileName = file.getName().toLowerCase();

        try (FileInputStream fis = new FileInputStream(file)) {
            if (fileName.endsWith(".docx")) {
                return parseDocx(fis);
            } else if (fileName.endsWith(".doc")) {
                return parseDoc(fis);
            } else {
                throw new IllegalArgumentException("Unsupported file type: " + fileName);
            }
        }
    }

    /**
     * 解析DOC文档并转换为Markdown格式
     * 支持标题、段落、表格
     *
     * @param fis FileInputStream
     * @return Markdown格式的文档内容
     */
    private static String parseDoc(FileInputStream fis) throws IOException {
        try (HWPFDocument doc = new HWPFDocument(fis)) {
            StringBuilder markdown = new StringBuilder();

            // 获取文档内容
            Range range = doc.getRange();

            // 处理文档段落和表格
            processDocContent(range, markdown);

            return markdown.toString();
        }
    }

    /**
     * 处理DOC文档的内容
     */
    private static void processDocContent(Range range, StringBuilder markdown) {
        // 先收集可能的标题段落
        List<DocTocEntry> tocEntries = new ArrayList<>();
        collectDocHeadings(range, tocEntries);

        // 如果有标题段落，生成目录
        if (!tocEntries.isEmpty()) {
            for (DocTocEntry entry : tocEntries) {
                // 添加缩进以表示层级
                StringBuilder indent = new StringBuilder();
                for (int i = 0; i < entry.level - 1; i++) {
                    indent.append("  ");
                }
                // 处理标题文本，确保它是有效的锚点ID
                String anchorId = entry.title.toLowerCase()
                        .replaceAll("[^\\w\\s-]", "") // 移除特殊字符
                        .replaceAll("\\s+", "-"); // 空格替换为连字符

                markdown.append(indent).append("- [")
                        .append(entry.title).append("](#")
                        .append(anchorId)
                        .append(")\n");
            }
            markdown.append("\n");
        }

        // 处理段落和表格
        int i = 0;
        while (i < range.numParagraphs()) {
            Paragraph paragraph = range.getParagraph(i);

            // 检查是否是表格的开始
            if (paragraph.isInTable()) {
                // 找到表格并处理它
                Table table = range.getTable(paragraph);
                processDocTable(table, markdown);

                // 跳过表格中的所有段落
                i += table.numParagraphs();
            } else {
                // 处理普通段落
                processDocParagraph(paragraph, markdown, tocEntries);
                i++;
            }
        }
    }

    /**
     * 收集DOC文档中的标题段落
     */
    private static void collectDocHeadings(Range range, List<DocTocEntry> tocEntries) {
        for (int i = 0; i < range.numParagraphs(); i++) {
            Paragraph paragraph = range.getParagraph(i);

            // 检查段落样式，尝试判断是否为标题
            int level = getHeadingLevel(paragraph);
            if (level > 0) {
                tocEntries.add(new DocTocEntry(paragraph.text().trim(), level));
            }
        }
    }

    /**
     * 获取段落的标题级别，如果不是标题返回0
     */
    private static int getHeadingLevel(Paragraph paragraph) {
        // 检查段落样式，尝试判断是否为标题
        int styleIndex = paragraph.getStyleIndex();

        // 判断样式是否为标题样式
        // 注意：这里的判断逻辑可能需要根据实际文档结构调整
        if (styleIndex >= 1 && styleIndex <= 9) {
            // 假设样式索引对应标题级别
            return styleIndex;
        }

        // 如果没有样式信息，尝试通过其他特征判断
        String text = paragraph.text().trim();
        if (!text.isEmpty()) {
            // 判断段落的字体大小和其他格式特征
            CharacterRun run = paragraph.getCharacterRun(0);
            if (run != null) {
                // 判断字体大小和粗体等特征
                if (run.isBold() && run.getFontSize() > 12) {
                    return 1; // 大字体粗体可能是一级标题
                } else if (run.isBold()) {
                    return 2; // 粗体可能是二级标题
                }
            }
        }

        return 0; // 不是标题
    }

    /**
     * 处理DOC文档中的段落
     */
    private static void processDocParagraph(Paragraph paragraph, StringBuilder markdown, List<DocTocEntry> tocEntries) {
        String text = paragraph.text().trim();
        if (text.isEmpty()) {
            markdown.append("\n");
            return;
        }

        // 检查是否是标题段落
        int level = getHeadingLevel(paragraph);
        if (level > 0) {
            // 生成标题标记
            StringBuilder heading = new StringBuilder();
            for (int i = 0; i < level; i++) {
                heading.append("#");
            }
            // 确保标题前有空行，这对Markdown渲染很重要
            if (!markdown.toString().endsWith("\n\n")) {
                markdown.append("\n");
            }
            markdown.append(heading).append(" ").append(text).append("\n\n");
        } else {
            // 检查是否是中文数字标题格式（如：一、二、三、等）
            if (text.matches("^[一二三四五六七八九十]+、.*")) {
                // 提取中文数字部分作为标题级别
                String prefix = text.substring(0, text.indexOf("、") + 1);
                String titleText = text.substring(text.indexOf("、") + 1).trim();

                // 根据前缀长度确定标题级别
                int chineseLevel = 2; // 默认为二级标题

                // 确保标题前有空行
                if (!markdown.toString().endsWith("\n\n")) {
                    markdown.append("\n");
                }

                // 生成标题标记
                StringBuilder heading = new StringBuilder();
                for (int i = 0; i < chineseLevel; i++) {
                    heading.append("#");
                }

                markdown.append(heading).append(" ").append(prefix).append(" ").append(titleText).append("\n\n");
            } else {
                // 处理段落文本格式
                processDocParagraphText(paragraph, markdown);
                markdown.append("\n\n");
            }
        }
    }

    /**
     * 处理DOC文档段落的文本格式
     */
    private static void processDocParagraphText(Paragraph paragraph, StringBuilder markdown) {
        // 处理段落中的文本格式
        for (int i = 0; i < paragraph.numCharacterRuns(); i++) {
            CharacterRun run = paragraph.getCharacterRun(i);
            String text = run.text();

            if (text != null && !text.trim().isEmpty()) {
                // 处理特殊字符转义
                text = escapeMarkdownSpecialChars(text);

                // 处理文本格式
                if (run.isBold()) {
                    text = "**" + text + "**";
                }
                if (run.isItalic()) {
                    text = "*" + text + "*";
                }
                // 旧版DOC不支持判断删除线，这里省略

                markdown.append(text);
            }
        }
    }

    /**
     * 处理DOC文档中的表格
     */
    private static void processDocTable(Table table, StringBuilder markdown) {
        if (table.numRows() == 0) {
            return;
        }

        // 获取表格的行数和列数
        int rowCount = table.numRows();
        int colCount = table.getRow(0).numCells();

        // 确保表格前有空行
        if (!markdown.toString().endsWith("\n\n")) {
            markdown.append("\n");
        }

        // 创建表头
        TableRow headerRow = table.getRow(0);
        markdown.append("| ");
        for (int i = 0; i < headerRow.numCells(); i++) {
            TableCell cell = headerRow.getCell(i);
            String cellText = cell.text().trim();
            // 处理特殊字符
            cellText = escapeMarkdownSpecialChars(cellText);
            markdown.append(cellText).append(" | ");
        }
        markdown.append("\n");

        // 添加分隔行
        markdown.append("| ");
        for (int i = 0; i < colCount; i++) {
            markdown.append("------- | ");
        }
        markdown.append("\n");

        // 添加数据行
        for (int i = 1; i < rowCount; i++) {
            TableRow row = table.getRow(i);
            markdown.append("| ");
            for (int j = 0; j < row.numCells(); j++) {
                TableCell cell = row.getCell(j);
                String cellText = cell.text().trim();
                // 处理特殊字符
                cellText = escapeMarkdownSpecialChars(cellText);
                markdown.append(cellText).append(" | ");
            }
            markdown.append("\n");
        }

        markdown.append("\n");
    }

    /**
     * DOC文档目录项类
     */
    private static class DocTocEntry {
        String title;
        int level;

        public DocTocEntry(String title, int level) {
            this.title = title;
            this.level = level;
        }
    }

    /**
     * 解析DOCX文档并转换为Markdown格式
     * 支持标题、段落、表格、图片和目录
     *
     * @param fis FileInputStream
     * @return Markdown格式的文档内容
     */
    private String parseDocx(FileInputStream fis) throws IOException {
        try (XWPFDocument document = new XWPFDocument(fis)) {
            StringBuilder markdown = new StringBuilder();
            Map<String, String> bookmarks = new HashMap<>();
            List<TocEntry> tocEntries = new ArrayList<>();

            // 第一遍扫描，收集所有书签和目录项
            collectBookmarksAndToc(document, bookmarks, tocEntries);

            // 如果有目录项，生成目录
            if (!tocEntries.isEmpty()) {
                for (TocEntry entry : tocEntries) {
                    // 添加缩进以表示层级
                    StringBuilder indent = new StringBuilder();
                    for (int i = 0; i < entry.level - 1; i++) {
                        indent.append("  ");
                    }
                    // 处理标题文本，确保它是有效的锚点ID
                    String anchorId = entry.title.toLowerCase()
                            .replaceAll("[^\\w\\s-]", "") // 移除特殊字符
                            .replaceAll("\\s+", "-"); // 空格替换为连字符

                    markdown.append(indent).append("- [")
                            .append(entry.title).append("](#")
                            .append(anchorId)
                            .append(")\n");
                }
                markdown.append("\n");
            }

            // 处理文档主体内容
            processDocumentBody(document, markdown);

            return markdown.toString();
        }
    }

    /**
     * 收集文档中的书签和目录项
     */
    private static void collectBookmarksAndToc(XWPFDocument document, Map<String, String> bookmarks, List<TocEntry> tocEntries) {
        // 处理段落中的书签
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            // 检查段落样式是否为标题
            String styleName = paragraph.getStyle();
            if (styleName != null && styleName.startsWith("Heading")) {
                try {
                    int level = Integer.parseInt(styleName.substring("Heading".length()).trim());
                    tocEntries.add(new TocEntry(paragraph.getText(), level));
                } catch (NumberFormatException e) {
                    // 忽略非标准标题样式
                }
            }

            // 收集书签
            for (CTBookmark bookmark : paragraph.getCTP().getBookmarkStartList()) {
                bookmarks.put(bookmark.getName(), paragraph.getText());
            }
        }
    }

    /**
     * 处理文档主体内容，包括段落、表格和图片
     */
    private void processDocumentBody(XWPFDocument document, StringBuilder markdown) throws IOException {
        // 处理文档主体
        for (IBodyElement element : document.getBodyElements()) {
            if (element instanceof XWPFParagraph) {
                processParagraph((XWPFParagraph) element, markdown);
            } else if (element instanceof XWPFTable) {
                processTable((XWPFTable) element, markdown);
            }
        }
    }

    /**
     * 处理段落元素，包括文本和图片
     */
    private void processParagraph(XWPFParagraph paragraph, StringBuilder markdown) {
        // 检查段落样式
        String styleName = paragraph.getStyle();
        String text = paragraph.getText().trim();
        // 正文内容是目录，跳过
        if (Objects.equals(text, "目录")) {
            return;
        }

        // 如果段落为空且没有图片，则添加空行
        if (text.isEmpty() && !containsImages(paragraph)) {
            markdown.append("\n");
            return;
        }

        // 处理标题
        if (styleName != null && styleName.startsWith("Heading")) {
            try {
                int level = Integer.parseInt(styleName.substring("Heading".length()).trim());

                // 确保标题前有空行
                if (!markdown.toString().endsWith("\n\n")) {
                    markdown.append("\n");
                }

                StringBuilder heading = new StringBuilder();
                for (int i = 0; i < level; i++) {
                    heading.append("#");
                }
                markdown.append(heading).append(" ").append(text).append("\n\n");
                return;
            } catch (NumberFormatException e) {
                // 如果解析失败，按普通段落处理
            }
        }

        // 检查是否是中文数字标题格式（如：一、二、三、等）
        if (text.matches("^[一二三四五六七八九十]+、.*")) {
            // 提取中文数字部分作为标题级别
            String prefix = text.substring(0, text.indexOf("、") + 1);
            String titleText = text.substring(text.indexOf("、") + 1).trim();

            // 根据前缀长度确定标题级别
            int chineseLevel = 2; // 默认为二级标题

            // 确保标题前有空行
            if (!markdown.toString().endsWith("\n\n")) {
                markdown.append("\n");
            }

            // 生成标题标记
            StringBuilder heading = new StringBuilder();
            for (int i = 0; i < chineseLevel; i++) {
                heading.append("#");
            }

            markdown.append(heading).append(" ").append(prefix).append(" ").append(titleText).append("\n\n");
            return;
        }

        // 处理段落中的图片
        List<XWPFRun> runs = paragraph.getRuns();
        boolean hasProcessedContent = false;

        for (XWPFRun run : runs) {
            List<XWPFPicture> pictures = run.getEmbeddedPictures();
            if (!pictures.isEmpty()) {
                for (XWPFPicture picture : pictures) {
                    // 获取图片尺寸信息
                    org.openxmlformats.schemas.drawingml.x2006.main.CTPositiveSize2D size = null;
                    try {
                        size = picture.getCTPicture().getSpPr().getXfrm().getExt();
                    } catch (Exception e) {
                        log.debug("无法获取图片尺寸", e);
                    }

                    String imageUrl = uploadPictureToOSS(picture);
                    if (imageUrl != null) {
                        // 如果有尺寸信息，将其作为自定义属性添加到markdown中
                        StringBuilder imgTag = new StringBuilder("![image");

                        if (size != null) {
                            // 将EMU单位转换为像素（EMU = English Metric Unit, 1 inch = 914400 EMU, 1 inch = 96 pixels）
                            long widthEmu = size.getCx();
                            long heightEmu = size.getCy();
                            int widthPx = (int) Math.round(widthEmu / 9525.0); // 9525 EMU = 1 pixel
                            int heightPx = (int) Math.round(heightEmu / 9525.0);

                            // 添加尺寸信息作为自定义属性
                            imgTag.append(" width=\"")
                                    .append(widthPx)
                                    .append("\" height=\"")
                                    .append(heightPx)
                                    .append("\" data-original-width=\"")
                                    .append(widthPx)
                                    .append("\" data-original-height=\"")
                                    .append(heightPx)
                                    .append("\"");
                        }

                        imgTag.append("](")
                                .append(imageUrl)
                                .append(")\n\n");

                        markdown.append(imgTag);
                        hasProcessedContent = true;
                    }
                }
            } else if (!run.getText(0).trim().isEmpty()) {
                // 处理文本样式
                String runText = run.getText(0);
                if (run.isBold()) {
                    runText = "**" + runText + "**";
                }
                if (run.isItalic()) {
                    runText = "*" + runText + "*";
                }
                if (run.isStrikeThrough()) {
                    runText = "~~" + runText + "~~";
                }
                markdown.append(runText);
                hasProcessedContent = true;
            }
        }

        // 如果段落有内容但没有被处理过，则添加普通文本
        if (!text.isEmpty() && !hasProcessedContent) {
            markdown.append(text);
        }

        markdown.append("\n\n");
    }

    /**
     * 检查段落是否包含图片
     */
    private static boolean containsImages(XWPFParagraph paragraph) {
        for (XWPFRun run : paragraph.getRuns()) {
            if (!run.getEmbeddedPictures().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理表格元素
     */
    private static void processTable(XWPFTable table, StringBuilder markdown) {
        if (table.getRows().isEmpty()) {
            return;
        }

        // 获取表格的行数和列数
        int rowCount = table.getRows().size();
        int colCount = table.getRow(0).getTableCells().size();

        // 确保表格前有空行
        if (!markdown.toString().endsWith("\n\n")) {
            markdown.append("\n");
        }

        // 创建表头
        XWPFTableRow headerRow = table.getRow(0);
        markdown.append("| ");
        for (XWPFTableCell cell : headerRow.getTableCells()) {
            String cellText = cell.getText().trim();
            // 处理特殊字符
            cellText = escapeMarkdownSpecialChars(cellText);
            markdown.append(cellText).append(" | ");
        }
        markdown.append("\n");

        // 添加分隔行
        markdown.append("| ");
        for (int i = 0; i < colCount; i++) {
            markdown.append("------- | ");
        }
        markdown.append("\n");

        // 添加数据行
        for (int i = 1; i < rowCount; i++) {
            XWPFTableRow row = table.getRow(i);
            markdown.append("| ");
            for (XWPFTableCell cell : row.getTableCells()) {
                String cellText = cell.getText().trim();
                // 处理特殊字符
                cellText = escapeMarkdownSpecialChars(cellText);
                markdown.append(cellText).append(" | ");
            }
            markdown.append("\n");
        }

        markdown.append("\n");
    }

    /**
     * 将图片上传到OSS并返回URL
     */
    private String uploadPictureToOSS(XWPFPicture picture) {
        try {
            byte[] pictureData = picture.getPictureData().getData();
            String fileExtension = picture.getPictureData().suggestFileExtension();

            // 创建临时文件
            final File tempFile = File.createTempFile("word_image_", "." + fileExtension);
            FileUtils.writeByteArrayToFile(tempFile, pictureData);

            // 使用SysFileServiceImpl的实例来上传文件
            final String imageUrl = uploadImageToOSS(tempFile);

            // 清理临时文件
            tempFile.delete();
            return imageUrl;
        } catch (Exception e) {
            log.error("上传图片到OSS失败", e);
            return null;
        }
    }


    /**
     * 上传图片到OSS
     */
    private String uploadImageToOSS(final File file) {
        try {
            final String fileName = saveTempFileToOSS(file, "");
            return filePreUrl + filePath + "?fileName=" + fileName + "&originFileName=" + fileName;
        } catch (Exception e) {
            log.error("上传图片到OSS失败", e);
            return null;
        }
    }

    /**
     * 目录项类，用于存储目录结构
     */
    private static class TocEntry {
        String title;
        int level;

        public TocEntry(String title, int level) {
            this.title = title;
            this.level = level;
        }
    }

    /**
     * 转义Markdown特殊字符
     * 处理常见的Markdown特殊字符，确保它们在最终文档中正确显示
     */
    private static String escapeMarkdownSpecialChars(String text) {
        if (text == null) {
            return "";
        }

        // 转义以下特殊字符: * _ ` # + - . ! [ ] ( ) { } < > | \
        return text
                .replace("|", "\\|")
                .replace("*", "\\*")
                .replace("_", "\\_")
                .replace("`", "\\`")
                // 只有在行首的#才需要转义
                .replaceAll("^(#+)", "\\\\$1")
                // 保留图片链接格式
                .replaceAll("(!\\[.*?\\]\\(.*?\\))", "IMGPLACEHOLDER")
                // 转义其他特殊字符
                .replace("[", "\\[")
                .replace("]", "\\]")
                .replace("(", "\\(")
                .replace(")", "\\)")
                // 恢复图片链接
                .replaceAll("IMGPLACEHOLDER", "$1");
    }

    /**
     * 根据文件名获取正确的Content-Type
     * 支持更多文件格式，提供更精确的MIME类型判断
     *
     * @param fileName 原始文件名
     * @param preview 是否为预览模式
     * @return 对应的Content-Type
     */
    private static String getContentTypeByFileName(final String fileName, final Boolean preview) {
        if (StrUtil.isBlank(fileName)) {
            return "application/octet-stream";
        }

        // 获取文件扩展名，转为小写进行比较
        final String extension = FileNameUtil.getSuffix(fileName).toLowerCase();

        // 图片文件
        switch (extension) {
            case "png":
                return MediaType.IMAGE_PNG_VALUE;
            case "jpg":
            case "jpeg":
                return MediaType.IMAGE_JPEG_VALUE;
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            case "svg":
                return "image/svg+xml";
            case "ico":
                return "image/x-icon";

            // 文档文件
            case "pdf":
                return MediaType.APPLICATION_PDF_VALUE;
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "rtf":
                return "application/rtf";

            // 压缩文件
            case "zip":
                return "application/zip";
            case "rar":
                return "application/x-rar-compressed";
            case "7z":
                return "application/x-7z-compressed";
            case "tar":
                return "application/x-tar";
            case "gz":
                return "application/gzip";

            // 文本文件
            case "txt":
                return "text/plain; charset=utf-8";
            case "html":
            case "htm":
                return "text/html; charset=utf-8";
            case "css":
                return "text/css; charset=utf-8";
            case "js":
                return "application/javascript; charset=utf-8";
            case "json":
                return "application/json; charset=utf-8";
            case "xml":
                return "application/xml; charset=utf-8";
            case "csv":
                return "text/csv; charset=utf-8";

            // 音频文件
            case "mp3":
                return "audio/mpeg";
            case "wav":
                return "audio/wav";
            case "ogg":
                return "audio/ogg";
            case "m4a":
                return "audio/mp4";

            // 视频文件
            case "mp4":
                return "video/mp4";
            case "avi":
                return "video/x-msvideo";
            case "mov":
                return "video/quicktime";
            case "wmv":
                return "video/x-ms-wmv";
            case "flv":
                return "video/x-flv";

            // 其他常见格式
            case "exe":
                return "application/x-msdownload";
            case "dmg":
                return "application/x-apple-diskimage";
            case "deb":
                return "application/x-debian-package";
            case "rpm":
                return "application/x-rpm";

            default:
                // 对于未知文件类型的处理
                if (preview != null && preview) {
                    // 预览模式下，如果是未知类型，尝试以PDF方式预览（适用于可转换的文档）
                    return MediaType.APPLICATION_PDF_VALUE;
                } else {
                    // 下载模式下，使用通用的二进制流类型
                    return "application/octet-stream";
                }
        }
    }
}

