package com.quantchi.knowledge.center.service;

import com.quantchi.knowledge.center.bean.dto.IndustryChainReportContentDTO;
import com.quantchi.knowledge.center.bean.dto.IndustryChainReportDTO;
import com.quantchi.knowledge.center.bean.vo.IndustryChainReportVO;

import java.util.List;

/**
 * 产业链报告服务接口
 *
 * <AUTHOR>
 * @date 2025/03/19
 */
public interface IndustryChainReportService {
    
    /**
     * 获取产业链报告目录
     * @return 报告目录
     */
    List<IndustryChainReportVO.TocEntryVO> getIndustryChainReportToc(final IndustryChainReportDTO reportDTO);
    
    /**
     * 获取产业链报告内容（仅Markdown内容）
     *
     * @return 报告Markdown内容
     */
    String getIndustryChainReportContent(final IndustryChainReportContentDTO reportContentDTO);
}
