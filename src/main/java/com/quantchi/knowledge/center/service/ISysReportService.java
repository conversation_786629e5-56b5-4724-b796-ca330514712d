package com.quantchi.knowledge.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.quantchi.knowledge.center.bean.bo.ReportDownloadBO;
import com.quantchi.knowledge.center.bean.entity.SysReport;
import com.quantchi.knowledge.center.bean.system.bo.SysReportUploadBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportSubmitBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportDownloadBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportBatchDeleteBO;
import com.quantchi.knowledge.center.bean.system.query.ReportQuery;
import com.quantchi.knowledge.center.bean.system.vo.SysReportDetailVO;
import com.quantchi.knowledge.center.bean.system.vo.SysReportPageVO;
import com.quantchi.knowledge.center.bean.system.vo.SysReportBatchDeleteResultVO;

import java.util.Map;

/**
 * <p>
 * 报告表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface ISysReportService extends IService<SysReport> {

    Boolean uploadReport(SysReportUploadBO reportUploadBO) throws Exception;

    Boolean editReport(SysReportUploadBO reportUploadBO);

    PageInfo<SysReportPageVO> myReportList(ReportQuery reportQuery);

    PageInfo<SysReportPageVO> reportMarket(ReportQuery reportQuery);

    Boolean publicReport(Long reportId);

    Boolean unShelfReport(Long reportId);

    Boolean deleteReport(Long reportId);

    SysReportBatchDeleteResultVO batchDeleteReport(SysReportBatchDeleteBO batchDeleteBO);

    Map<String, Object> getOption();

    SysReportDetailVO reportInfo(Long reportId);

    String downloadAIReport(ReportDownloadBO bo, final Long sysUserDownloadId, final long userId);

    Boolean submitReport(SysReportSubmitBO reportSubmitBO);

    Integer downloadReport(SysReportDownloadBO reportDownloadBO) throws Exception;

    boolean renameReport(Long reportId, String newTitle, Long userId) throws IllegalArgumentException;
}
