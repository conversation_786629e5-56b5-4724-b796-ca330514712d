package com.quantchi.knowledge.center.service.impl;

import com.quantchi.knowledge.center.bean.entity.SysAiReport;
import com.quantchi.knowledge.center.bean.entity.SysReport;
import com.quantchi.knowledge.center.dao.mysql.SysReportMapper;
import com.quantchi.knowledge.center.service.ISysAiReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import static com.quantchi.knowledge.center.util.Word2PdfUtil.getLicense;
import static com.quantchi.knowledge.center.util.Word2PdfUtil.setFontsSources;

/**
 * <p>
 * 报告数据保存 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ReportPreserveService {

    private final ISysAiReportService sysAiReportService;

    private final SysReportMapper sysReportMapper;

    /**
     * 禁用SSL证书验证，解决图片链接的SSL问题
     * 注意：这种方法在生产环境中应谨慎使用，因为它会禁用所有的SSL证书验证
     */
    private void disableSSLCertificateValidation() {
        try {
            // 创建一个信任所有证书的TrustManager
            javax.net.ssl.TrustManager[] trustAllCerts = new javax.net.ssl.TrustManager[]{
                    new javax.net.ssl.X509TrustManager() {
                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }

                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] certs, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] certs, String authType) {
                        }
                    }
            };

            // 安装全局信任管理器
            javax.net.ssl.SSLContext sc = javax.net.ssl.SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            javax.net.ssl.HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // 创建全部信任的主机名验证器
            javax.net.ssl.HostnameVerifier allHostsValid = new javax.net.ssl.HostnameVerifier() {
                @Override
                public boolean verify(String hostname, javax.net.ssl.SSLSession session) {
                    return true;
                }
            };

            // 安装默认的主机名验证器
            javax.net.ssl.HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // 设置OpenHtmlToPdf的SSL属性
            System.setProperty("com.openhtmltopdf.ssl.ignore-certificate-errors", "true");
        } catch (Exception e) {
            log.error("禁用SSL证书验证失败", e);
        }
    }

    /**
     * 处理HTML中的图片链接，下载图片并将其转换为数据编码格式嵌入到HTML中
     *
     * @param document Jsoup文档对象
     */
    private void processImageLinks(Document document) {
        // 查找所有图片元素
        for (org.jsoup.nodes.Element img : document.select("img")) {
            String src = img.attr("src");
            if (src != null && !src.isEmpty()) {
                try {
                    // 保存原始的宽度和高度属性
                    String width = img.attr("width");
                    String height = img.attr("height");
                    String style = img.attr("style");

                    // 下载图片
                    byte[] imageData = downloadImage(src);
                    if (imageData != null && imageData.length > 0) {
                        // 判断图片类型
                        String imageType = getImageMimeType(src);

                        // 将图片转换为Base64编码并嵌入到HTML中
                        String base64Image = java.util.Base64.getEncoder().encodeToString(imageData);
                        String dataUrl = "data:" + imageType + ";base64," + base64Image;

                        // 替换原始的图片URL
                        img.attr("src", dataUrl);

                        // 确保图片尺寸信息被保留
                        if (width != null && !width.isEmpty()) {
                            img.attr("width", width);
                        }
                        if (height != null && !height.isEmpty()) {
                            img.attr("height", height);
                        }

                        // 如果没有明确的宽高属性，但有样式，确保样式被保留
                        if ((width.isEmpty() || height.isEmpty()) && style != null && !style.isEmpty()) {
                            img.attr("style", style);
                        }

                        // 如果既没有宽高属性也没有样式，添加一个默认样式以控制图片大小
                        if (width.isEmpty() && height.isEmpty() && style.isEmpty()) {
                            // 添加一个合理的最大宽度，防止图片过大
                            img.attr("style", "width: 100%;");
                        }

                        log.info("将图片链接 {} 转换为嵌入式数据并保留尺寸信息", src);
                    }
                } catch (Exception e) {
                    log.error("处理图片链接失败: {}", src, e);
                }
            }
        }
    }

    /**
     * 下载图片
     *
     * @param imageUrl 图片URL
     * @return 图片数据字节数组
     */
    private byte[] downloadImage(String imageUrl) {
        try {
            java.net.URL url = new java.net.URL(imageUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

            try (java.io.InputStream inputStream = connection.getInputStream();
                 java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream()) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                return outputStream.toByteArray();
            }
        } catch (Exception e) {
            log.error("下载图片失败: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 根据图片URL获取MIME类型
     *
     * @param imageUrl 图片URL
     * @return MIME类型字符串
     */
    private String getImageMimeType(String imageUrl) {
        String lowerUrl = imageUrl.toLowerCase();
        if (lowerUrl.endsWith(".png")) {
            return "image/png";
        } else if (lowerUrl.endsWith(".jpg") || lowerUrl.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerUrl.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerUrl.endsWith(".svg")) {
            return "image/svg+xml";
        } else if (lowerUrl.endsWith(".webp")) {
            return "image/webp";
        } else {
            // 默认使用PNG
            return "image/png";
        }
    }

    public void exportReportToWordWithAspose(Long reportId, HttpServletResponse response) throws IOException {
        // 在开始转换前禁用SSL证书验证，解决图片链接的SSL问题
        disableSSLCertificateValidation();
        // 获取报告数据
        final SysReport sysReport = sysReportMapper.selectById(reportId);
        SysAiReport report = sysAiReportService.getByReportId(reportId);
        if (report == null) {
            throw new RuntimeException("报告不存在");
        }
        setFontsSources();
        if (!getLicense()) { // 验证License 若不验证则转化出的pdf文档会有水印产生
            return;
        }
        final String title = sysReport.getTitle();

        // 获取HTML内容
        String htmlContent = report.getContent();
        if (htmlContent == null || htmlContent.isEmpty()) {
            throw new RuntimeException("报告内容为空");
        }

        // 解析HTML内容
        Document document = Jsoup.parse(htmlContent);

        // 处理HTML中的图片链接，下载图片并嵌入
        processImageLinks(document);

        // 准备HTML内容，添加正确的字符编码声明
        String cleanedHtml = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "<meta charset=\"UTF-8\">\n" +
                "<title>" + title + "</title>\n" +
                "<style>\n" +
                "body { font-family: 'SimSun', 'Arial', sans-serif; }\n" +
                "</style>\n" +
                "</head>\n" +
                "<body>\n" +
                document.body().html() + "\n" +
                "</body>\n" +
                "</html>";

        try {
            // 创建一个临时文件来存储HTML内容
            java.io.File tempHtmlFile = java.io.File.createTempFile("report_", ".html");
            try (java.io.OutputStreamWriter writer = new java.io.OutputStreamWriter(
                    new java.io.FileOutputStream(tempHtmlFile), StandardCharsets.UTF_8)) {
                writer.write(cleanedHtml);
            }

            // 使用Aspose.Words加载HTML文件
            com.aspose.words.Document doc = new com.aspose.words.Document(tempHtmlFile.getAbsolutePath());

            // 设置页面大小为A4
            com.aspose.words.PageSetup pageSetup = doc.getFirstSection().getPageSetup();
            pageSetup.setPaperSize(com.aspose.words.PaperSize.A4);
            pageSetup.setOrientation(com.aspose.words.Orientation.PORTRAIT);

            // 设置页边距（单位：磅，1英寸=72磅）
            pageSetup.setTopMargin(72.0);
            pageSetup.setBottomMargin(72.0);
            pageSetup.setLeftMargin(72.0);
            pageSetup.setRightMargin(72.0);

            // 设置响应头
            String fileName = URLEncoder.encode(title + ".docx", StandardCharsets.UTF_8.name());
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 保存到响应输出流
            try (OutputStream outputStream = response.getOutputStream()) {
                doc.save(outputStream, com.aspose.words.SaveFormat.DOCX);
                outputStream.flush();
            }

            // 删除临时文件
            tempHtmlFile.delete();

            log.info("使用Aspose.Words成功导出报告: {}", title);
        } catch (Exception e) {
            log.error("使用Aspose.Words导出Word文档失败", e);
            throw new IOException("导出Word文档失败: " + e.getMessage(), e);
        }
    }

    public boolean deleteReport(Long reportId, Long userId) {
        // 获取报告数据
        final SysReport report = sysReportMapper.selectById(reportId);
        final SysAiReport sysAiReport = sysAiReportService.getByReportId(reportId);
        if (report == null) {
            log.error("删除报告失败：报告不存在，reportId={}", reportId);
            return false;
        }

        // 验证报告所有者
        if (!report.getOwnerId().equals(userId)) {
            log.error("删除报告失败：用户无权限删除该报告，reportId={}，userId={}", reportId, userId);
            return false;
        }


        // 执行删除操作
        try {
            final int i = sysReportMapper.deleteById(reportId);
            if (sysAiReport != null) {
                sysAiReportService.removeById(sysAiReport.getId());
            }
            return true;
        } catch (Exception e) {
            log.error("删除报告失败：数据库操作异常", e);
            return false;
        }
    }

}
