package com.quantchi.knowledge.center.service.impl;

import com.quantchi.knowledge.center.bean.entity.SysAgentConfig;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.vo.DifyApplicationParametersVO;
import com.quantchi.knowledge.center.service.IAgentConfigService;
import com.quantchi.knowledge.center.service.IDifyApiService;
import com.quantchi.knowledge.center.util.DifyParametersConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;
import cn.hutool.http.HttpUtil;
import org.springframework.http.HttpHeaders;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;

/**
 * Dify API 服务实现
 * 
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DifyApiServiceImpl implements IDifyApiService {

    private final IAgentConfigService agentConfigService;
    
    @Value("${dify.api.parameters-path}")
    private String parametersPath;
    
    @Override
    public DifyApplicationParametersVO getApplicationParameters(final String agentKey) {
        // 获取智能体配置
        final SysAgentConfig sysAgentConfig = agentConfigService.getApiKeyByAgentKey(agentKey);
        if (sysAgentConfig == null) {
            log.error("无法找到智能体配置: {}", agentKey);
            throw new BusinessException("无法找到智能体配置");
        }
        
        // 构建API请求
        final String apiUrl = UriComponentsBuilder
                .fromHttpUrl(sysAgentConfig.getDomainUrl())
                .path(parametersPath)
                .build()
                .toUriString();
        
        log.info("请求Dify应用参数: {}, API Key: {}", apiUrl, sysAgentConfig.getApiKey());
        
        try {
            // 使用hutool的HttpUtil发送请求
            final String authHeader = "Bearer " + sysAgentConfig.getApiKey();
            final String responseBody = HttpUtil.createGet(apiUrl)
                    .header(HttpHeaders.AUTHORIZATION, authHeader)
                    .header(HttpHeaders.ACCEPT, "application/json")
                    .execute()
                    .body();
            
            // 使用Jackson ObjectMapper将响应解析为Map
            @SuppressWarnings("unchecked")
            final Map<String, Object> response = new ObjectMapper().readValue(responseBody, Map.class);

            // 转换为VO对象
            return DifyParametersConverter.toVO(response);
        } catch (Exception e) {
            log.error("获取Dify应用参数失败", e);
            throw new BusinessException("获取Dify应用参数失败: " + e.getMessage());
        }
    }
}
