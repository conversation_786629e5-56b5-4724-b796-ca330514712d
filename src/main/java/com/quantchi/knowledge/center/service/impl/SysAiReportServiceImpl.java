package com.quantchi.knowledge.center.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.knowledge.center.bean.entity.SysAiReport;
import com.quantchi.knowledge.center.bean.entity.SysFile;
import com.quantchi.knowledge.center.bean.entity.SysReport;
import com.quantchi.knowledge.center.bean.entity.SysUser;
import com.quantchi.knowledge.center.bean.enums.AiReportStatusEnum;
import com.quantchi.knowledge.center.bean.enums.ReportAuditStatusEnum;
import com.quantchi.knowledge.center.bean.enums.ReportDisplayStatusEnum;
import com.quantchi.knowledge.center.bean.enums.ReportTypeEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.system.bo.AiReportCreateBO;
import com.quantchi.knowledge.center.bean.system.bo.AiReportGenerateBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportSaveBO;
import com.quantchi.knowledge.center.bean.system.vo.AiReportDetailVO;
import com.quantchi.knowledge.center.bean.system.vo.AiReportStreamResponse;
import com.quantchi.knowledge.center.bean.vo.TocEntryVO;
import com.quantchi.knowledge.center.config.ModelParamConfig;
import com.quantchi.knowledge.center.dao.mysql.SysAiReportMapper;
import com.quantchi.knowledge.center.dao.mysql.SysUserMapper;
import com.quantchi.knowledge.center.service.ISysAiReportService;
import com.quantchi.knowledge.center.service.ISysFileService;
import com.quantchi.knowledge.center.service.ISysReportService;
import com.quantchi.knowledge.center.util.ExternalReportApiClient;
import com.quantchi.knowledge.center.util.MarkdownTocConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;


/**
 * <p>
 * AI报告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysAiReportServiceImpl extends ServiceImpl<SysAiReportMapper, SysAiReport> implements ISysAiReportService {

    @Lazy
    private final ISysReportService sysReportService;
    private final ISysFileService sysFileService;
    private final SysUserMapper sysUserMapper;
    private final ModelParamConfig modelParamConfig;
    private final ExternalReportApiClient externalReportApiClient;

    @Override
    public List<TocEntryVO> generateOutline(final AiReportCreateBO createBO) {
        try {
            final Long templateFileId = createBO.getTemplateFileId();

            // 调用外部接口生成大纲
            final String markdownOutline = externalReportApiClient.callOutlineApi(createBO.getTitle(), createBO.getDescription(),
                    createBO.getTheme(), templateFileId);

            // 将markdown格式转换为TocEntryVO列表
            final List<TocEntryVO> tocEntries = MarkdownTocConverter.convertMarkdownToTocEntries(markdownOutline);

            return tocEntries;

        } catch (final Exception e) {
            log.error("生成大纲失败", e);
            throw new BusinessException("生成大纲失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateAiReport(final AiReportGenerateBO generateBO) {
        final long loginId = StpUtil.getLoginIdAsLong();
        final SysUser user = sysUserMapper.selectById(loginId);

        // 1. 创建基础报告记录
        final SysReport sysReport = new SysReport();
        sysReport.setTitle(generateBO.getTitle());
        sysReport.setType(ReportTypeEnum.AI_REPORT.getId());
        sysReport.setDisplayStatus(ReportDisplayStatusEnum.NOT_PUBLIC.getId());
        sysReport.setAuditStatus(ReportAuditStatusEnum.ONE.getId());
        sysReport.setOwnerId(loginId);
        sysReport.setOwnerName(user.getMemberNickName());
        sysReport.setPublishDate(new Date());
        sysReport.setIsValid(true);
        sysReport.setClickTimes(0);
        sysReport.setDownloadTimes(0);

        final boolean saveReport = sysReportService.save(sysReport);
        if (!saveReport) {
            throw new BusinessException("创建报告失败");
        }

        // 2. 创建AI报告扩展记录
        final SysAiReport aiReport = new SysAiReport();
        aiReport.setReportId(sysReport.getId());
        aiReport.setDescription(generateBO.getDescription());
        aiReport.setTheme(generateBO.getTheme());
        aiReport.setTemplateFileId(generateBO.getTemplateFileId());

        // 如果有模板文件，获取文件名
        if (generateBO.getTemplateFileId() != null) {
            try {
                final SysFile templateFile = sysFileService.getById(generateBO.getTemplateFileId());
                if (templateFile != null) {
                    aiReport.setTemplateFileName(templateFile.getOriName());
                }
            } catch (final Exception e) {
                log.warn("获取模板文件名失败", e);
            }
        }

        // 保存大纲
        final String outlineJson = JSON.toJSONString(generateBO.getOutline());
        aiReport.setOutline(outlineJson);
        aiReport.setOutlineStatus(AiReportStatusEnum.OUTLINE_GENERATED.getId());
        aiReport.setContentStatus(AiReportStatusEnum.CONTENT_GENERATING.getId());
        aiReport.setCreateTime(new Date());
        aiReport.setUpdateTime(new Date());
        aiReport.setIsValid(true);

        final boolean saveAiReport = save(aiReport);
        if (!saveAiReport) {
            throw new BusinessException("创建AI报告扩展信息失败");
        }

        try {

            // 调用AI生成报告内容
            final String content = "";
            // 更新报告内容和状态
            aiReport.setContent(content);
            aiReport.setContentStatus(AiReportStatusEnum.CONTENT_GENERATED.getId());
            aiReport.setUpdateTime(new Date());
            updateById(aiReport);

            // 更新基础报告的摘要
            final String summary = generateSummary(content);
            sysReport.setSummarize(summary);
            sysReport.setUpdateTime(new Date());
            sysReportService.updateById(sysReport);

            return content;

        } catch (final Exception e) {
            log.error("生成报告内容失败", e);
            // 更新失败状态
            aiReport.setContentStatus(AiReportStatusEnum.CONTENT_FAILED.getId());
            aiReport.setUpdateTime(new Date());
            updateById(aiReport);
            throw new BusinessException("生成报告失败：" + e.getMessage());
        }
    }


    @Override
    public Flux<AiReportStreamResponse> generateAiReportStream(final AiReportGenerateBO generateBO) {
        final long loginId = StpUtil.getLoginIdAsLong();
        final SysUser user = sysUserMapper.selectById(loginId);

        // 1. 创建基础报告记录
        final SysReport sysReport = new SysReport();
        sysReport.setTitle(generateBO.getTitle());
        sysReport.setType(ReportTypeEnum.AI_REPORT.getId());
        sysReport.setDisplayStatus(ReportDisplayStatusEnum.NOT_PUBLIC.getId());
        sysReport.setAuditStatus(ReportAuditStatusEnum.ONE.getId());
        sysReport.setOwnerId(loginId);
        sysReport.setOwnerName(user.getMemberNickName());
        sysReport.setPublishDate(new Date());
        sysReport.setIsValid(true);
        sysReport.setClickTimes(0);
        sysReport.setDownloadTimes(0);

        final boolean saveReport = sysReportService.save(sysReport);
        if (!saveReport) {
            return Flux.error(new BusinessException("创建报告失败"));
        }

        // 2. 创建AI报告扩展记录
        final SysAiReport aiReport = new SysAiReport();
        aiReport.setReportId(sysReport.getId());
        aiReport.setDescription(generateBO.getDescription());
        aiReport.setTheme(generateBO.getTheme());
        aiReport.setTemplateFileId(generateBO.getTemplateFileId());

        // 如果有模板文件，获取文件名
        if (generateBO.getTemplateFileId() != null) {
            try {
                final SysFile templateFile = sysFileService.getById(generateBO.getTemplateFileId());
                if (templateFile != null) {
                    aiReport.setTemplateFileName(templateFile.getOriName());
                }
            } catch (final Exception e) {
                log.warn("获取模板文件名失败", e);
            }
        }

        // 保存大纲
        final String outlineJson = JSON.toJSONString(generateBO.getOutline());
        aiReport.setOutline(outlineJson);
        aiReport.setOutlineStatus(AiReportStatusEnum.OUTLINE_GENERATED.getId());
        aiReport.setContentStatus(AiReportStatusEnum.CONTENT_GENERATING.getId());
        aiReport.setCreateTime(new Date());
        aiReport.setUpdateTime(new Date());
        aiReport.setIsValid(true);

        final boolean saveAiReport = save(aiReport);
        if (!saveAiReport) {
            return Flux.error(new BusinessException("创建AI报告扩展信息失败"));
        }

        // 3. 将大纲转换为文本格式
        final String outlineContent = MarkdownTocConverter.convertTocEntriesToMarkdown(generateBO.getOutline());

        // 4. 调用流式API生成报告内容
        final AtomicReference<StringBuilder> contentBuilder = new AtomicReference<>(new StringBuilder());

        return externalReportApiClient.callStreamReportApiV2(
                generateBO.getTitle(),
                outlineContent,
                generateBO.getTheme()
        )
        // 切换到弹性线程池，避免阻塞事件循环线程
        .publishOn(Schedulers.boundedElastic())
        .doOnSubscribe(subscription -> {
            log.info("开始流式生成AI报告，reportId: {}", sysReport.getId());
        })
        .doOnNext(response -> {
            log.debug("收到流式响应: event={}, answer长度={}", response.getEvent(),
                     response.getAnswer() != null ? response.getAnswer().length() : 0);
            // 累积内容
            if ("message".equals(response.getEvent()) && response.getAnswer() != null) {
                contentBuilder.get().append(response.getAnswer());
            }
        })
        .concatWith(Flux.defer(() -> {
            // 在流结束时，生成最终的结束事件
            try {
                final String fullContent = contentBuilder.get().toString();
                log.info("流式输出完成，累积内容长度: {}", fullContent.length());

                // 更新报告内容和状态
                aiReport.setContent(fullContent);
                aiReport.setContentStatus(AiReportStatusEnum.CONTENT_GENERATED.getId());
                aiReport.setUpdateTime(new Date());
                updateById(aiReport);

                // 更新基础报告的摘要
                final String summary = generateSummary(fullContent);
                sysReport.setSummarize(summary);
                sysReport.setUpdateTime(new Date());
                sysReportService.updateById(sysReport);

                log.info("流式AI报告生成完成，reportId: {}, 内容长度: {}", sysReport.getId(), fullContent.length());

                // 创建最终的结束事件
                final AiReportStreamResponse endResponse = AiReportStreamResponse.createEndEvent(
                        null, // taskId
                        String.valueOf(System.currentTimeMillis()), // messageId
                        null, // conversationId
                        sysReport.getId(), // reportId
                        fullContent // fullContent
                );
                log.info("发送最终响应，reportId: {}", sysReport.getId());
                return Flux.just(endResponse);
            } catch (final Exception e) {
                log.error("保存流式生成的报告内容失败", e);
                // 更新失败状态
                aiReport.setContentStatus(AiReportStatusEnum.CONTENT_FAILED.getId());
                aiReport.setUpdateTime(new Date());
                updateById(aiReport);
                
                // 返回错误的结束事件
                final AiReportStreamResponse errorResponse = new AiReportStreamResponse();
                errorResponse.setEvent("message_end");
                errorResponse.setAnswer("保存报告失败：" + e.getMessage());
                errorResponse.setIsEnd(true);
                errorResponse.setCreatedAt(System.currentTimeMillis());
                return Flux.just(errorResponse);
            }
        }))
        .doOnError(e -> {
            log.error("流式生成AI报告失败", e);
        })
        .onErrorResume(e -> {
            log.error("流式生成AI报告失败，返回错误响应", e);
            // 更新失败状态
            aiReport.setContentStatus(AiReportStatusEnum.CONTENT_FAILED.getId());
            aiReport.setUpdateTime(new Date());
            updateById(aiReport);

            // 返回错误响应
            final AiReportStreamResponse errorResponse = new AiReportStreamResponse();
            errorResponse.setEvent("error");
            errorResponse.setAnswer("生成报告失败：" + e.getMessage());
            errorResponse.setIsEnd(true);
            errorResponse.setCreatedAt(System.currentTimeMillis());
            return Flux.just(errorResponse);
        });
    }

    @Override
    public AiReportDetailVO getAiReportDetail(final Long reportId) {
        final SysAiReport aiReport = getByReportId(reportId);
        if (aiReport == null) {
            throw new BusinessException("AI报告不存在");
        }

        final SysReport report = sysReportService.getById(reportId);
        if (report == null) {
            throw new BusinessException("报告不存在");
        }

        final AiReportDetailVO detailVO = new AiReportDetailVO();
        BeanUtils.copyProperties(aiReport, detailVO);
        final String content = aiReport.getContent();
        detailVO.setPreserveData(content);
        detailVO.setId(reportId);
        detailVO.setTitle(report.getTitle());
        detailVO.setDisplayStatus(report.getDisplayStatus());

        // 解析大纲JSON为TocEntryVO列表
        if (CharSequenceUtil.isNotBlank(aiReport.getOutline())) {
            try {
                final List<TocEntryVO> tocEntries = JSON.parseArray(aiReport.getOutline(), TocEntryVO.class);
                detailVO.setOutline(tocEntries);
            } catch (final Exception e) {
                log.warn("解析大纲JSON失败", e);
            }
        }

        return detailVO;
    }

    /**
     * 根据报告ID获取AI报告
     */
    @Override
    public SysAiReport getByReportId(final Long reportId) {
        final LambdaQueryWrapper<SysAiReport> queryWrapper = Wrappers.<SysAiReport>lambdaQuery()
                .eq(SysAiReport::getReportId, reportId)
                .eq(SysAiReport::getIsValid, true);
        return getOne(queryWrapper);
    }

    /**
     * 解析模板文件内容
     */
    private String parseTemplateFile(final Long templateFileId) throws IOException {
        final SysFile templateFile = sysFileService.getById(templateFileId);
        if (templateFile == null) {
            return null;
        }

        // 这里可以调用现有的文件解析方法
        // 参考SysReportServiceImpl中的analysisFileContent方法
        // 由于代码较长，这里简化处理
        return "模板文件内容解析功能待实现";
    }

    /**
     * 生成报告摘要
     * 从完整内容中提取前50个字符作为摘要
     *
     * @param fullContent 完整内容
     * @return 摘要
     */
    private String generateSummary(final String fullContent) {
        if (CharSequenceUtil.isBlank(fullContent)) {
            return "";
        }

        // 移除HTML标签和多余的空白字符
        final String cleanContent = fullContent.replaceAll("<[^>]+>", "")
                .replaceAll("\\s+", " ")
                .trim();

        // 截取前50个字符作为摘要
        if (cleanContent.length() <= 50) {
            return cleanContent;
        }

        return cleanContent.substring(0, 50) + "...";
    }

    /**
     * 调用智谱AI同步接口
     */
    private String zhipuSyncCall(final String prompt) {
        try {
            // 构建请求体
            final JSONObject requestBody = new JSONObject();
            requestBody.put("model", "glm-4-flash-250414");

            final JSONObject message = new JSONObject();
            message.put("role", "user");
            message.put("content", prompt);

            final JSONObject[] messages = {message};
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", 4000);
            requestBody.put("temperature", 0.7);

            // 发送请求
            final HttpResponse response = HttpRequest.post(modelParamConfig.getOneApiUrl())
                    .header("Authorization", modelParamConfig.getDpAccessToken())
                    .header("Content-Type", "application/json")
                    .body(requestBody.toJSONString())
                    .timeout(60000)
                    .execute();

            if (response.getStatus() != 200) {
                throw new BusinessException("AI接口调用失败：" + response.body());
            }

            final JSONObject responseBody = JSON.parseObject(response.body());
            final String content = responseBody.getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message")
                    .getString("content");

            return content;

        } catch (final Exception e) {
            log.error("调用智谱AI失败", e);
            throw new BusinessException("AI服务调用失败：" + e.getMessage());
        }
    }

    /**
     * 调用大模型生成摘要
     *
     * @param fileContent 文件内容
     * @return 摘要
     */
    private String generateSummaryByAI(final String fileContent) {
        try {
            // 构建提示词
            final String prompt = buildSummaryPrompt(fileContent);

            // 调用智谱AI
            return zhipuSyncCallForSummary(prompt);

        } catch (final Exception e) {
            log.error("调用大模型生成摘要失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建摘要生成的提示词
     *
     * @param fileContent 文件内容
     * @return 提示词
     */
    private String buildSummaryPrompt(final String fileContent) {
        // 限制文件内容长度，避免超过大模型token限制
        String content = fileContent;
        if (content.length() > 8000) {
            content = content.substring(0, 8000) + "...";
        }

        return "请为以下报告内容生成一段不超过20字的摘要，要求简洁明了，突出核心内容和主要观点，要求是纯文本，不要有 markdown 相关格式：\n\n" + content;
    }

    /**
     * 调用智谱AI同步接口（用于摘要生成）
     *
     * @param prompt 提示词
     * @return AI响应内容
     */
    private String zhipuSyncCallForSummary(final String prompt) {
        try {
            // 构建请求体
            final JSONObject requestBody = new JSONObject();
            requestBody.put("model", "glm-4-flash-250414");

            final JSONObject message = new JSONObject();
            message.put("role", "user");
            message.put("content", prompt);

            final JSONObject[] messages = {message};
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", 50); // 调整为适合20字摘要的长度
            requestBody.put("temperature", 0.7);

            // 发送请求，使用现有的配置
            final HttpResponse response = HttpRequest.post(modelParamConfig.getOneApiUrl())
                    .header("Authorization", modelParamConfig.getDpAccessToken())
                    .header("Content-Type", "application/json")
                    .body(requestBody.toJSONString())
                    .timeout(30000)
                    .execute();

            if (response.getStatus() != 200) {
                throw new BusinessException("AI接口调用失败：" + response.body());
            }

            final JSONObject responseBody = JSON.parseObject(response.body());
            final String content = responseBody.getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message")
                    .getString("content");

            return content;

        } catch (final Exception e) {
            log.error("调用智谱AI失败", e);
            throw new BusinessException("AI服务调用失败：" + e.getMessage());
        }
    }

    /**
     * 异步生成摘要并更新报告
     *
     * @param reportId 报告ID
     * @param content 报告内容
     * @param reportTitle 报告标题（用于日志）
     */
    private void generateSummaryAsync(final Long reportId, final String content, final String reportTitle) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始异步生成报告摘要，reportId: {}, title: {}", reportId, reportTitle);

                final String summary = generateSummaryByAI(content);
                if (CharSequenceUtil.isNotBlank(summary)) {
                    // 更新报告摘要
                    final SysReport sysReport = sysReportService.getById(reportId);
                    if (sysReport != null) {
                        sysReport.setSummarize(summary);
                        sysReport.setUpdateTime(new Date());
                        sysReportService.updateById(sysReport);
                        log.info("报告摘要异步生成成功，reportId: {}, title: {}, summary: {}",
                                reportId, reportTitle, summary);
                    } else {
                        sysReport.setSummarize(reportTitle);
                        log.warn("报告不存在，无法更新摘要，reportId: {}", reportId);
                    }
                } else {
                    log.warn("报告摘要异步生成失败，AI返回空内容，reportId: {}, title: {}",
                            reportId, reportTitle);
                }
            } catch (final Exception e) {
                log.error("异步生成报告摘要失败，reportId: {}, title: {}", reportId, reportTitle, e);
            }
        }).exceptionally(throwable -> {
            log.error("异步生成报告摘要任务执行异常，reportId: {}, title: {}", reportId, reportTitle, throwable);
            return null;
        });
    }

    @Override
    public Boolean saveReport(final SysReportSaveBO saveBO) {
        try {
            // 1. 根据reportId查找对应的AI报告记录
            final SysAiReport aiReport = getByReportId(saveBO.getReportId());
            if (aiReport == null) {
                throw new BusinessException("AI报告不存在");
            }

            // 2. 更新AI报告的content字段
            aiReport.setContent(saveBO.getPreserveData());

            // 3. 如果提供了大纲，保存大纲信息
            if (CollUtil.isNotEmpty(saveBO.getOutline())) {
                final String outlineJson = JSON.toJSONString(saveBO.getOutline());
                aiReport.setOutline(outlineJson);
            }

            aiReport.setUpdateTime(new Date());
            final boolean updateAiReport = updateById(aiReport);

            // 4. 更新基础报告表的信息
            final SysReport sysReport = sysReportService.getById(saveBO.getReportId());
            if (sysReport != null) {
                boolean needUpdateSysReport = false;

                // 如果提供了标题，更新标题
                if (CharSequenceUtil.isNotBlank(saveBO.getTitle())) {
                    sysReport.setTitle(saveBO.getTitle());
                    needUpdateSysReport = true;
                }

                // 如果有报告内容，异步生成并更新摘要
                if (CharSequenceUtil.isNotBlank(saveBO.getPreserveData())) {
                    // 异步生成摘要，不阻塞主流程
                    generateSummaryAsync(saveBO.getReportId(), saveBO.getPreserveData(), sysReport.getTitle());
                }

                if (needUpdateSysReport) {
                    sysReport.setUpdateTime(new Date());
                    sysReportService.updateById(sysReport);
                }
            }

            log.info("保存报告编辑内容成功，reportId: {}", saveBO.getReportId());
            return updateAiReport;

        } catch (final Exception e) {
            log.error("保存报告编辑内容失败，reportId: {}", saveBO.getReportId(), e);
            throw new BusinessException("保存报告失败：" + e.getMessage());
        }
    }
}
