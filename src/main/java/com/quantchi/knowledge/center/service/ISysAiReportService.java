package com.quantchi.knowledge.center.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.knowledge.center.bean.entity.SysAiReport;
import com.quantchi.knowledge.center.bean.system.bo.AiReportCreateBO;
import com.quantchi.knowledge.center.bean.system.bo.AiReportGenerateBO;
import com.quantchi.knowledge.center.bean.system.bo.SysReportSaveBO;
import com.quantchi.knowledge.center.bean.system.vo.AiReportDetailVO;
import com.quantchi.knowledge.center.bean.system.vo.AiReportStreamResponse;
import com.quantchi.knowledge.center.bean.vo.TocEntryVO;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * <p>
 * AI报告表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@DS("sys")
public interface ISysAiReportService extends IService<SysAiReport> {

    /**
     * 生成报告大纲
     * 调用外部接口获取大纲，并转换为TocEntryVO格式
     * 此时还没有reportId，只是生成大纲供用户编辑
     *
     * @param createBO 报告基础信息
     * @return 生成的大纲内容(TocEntryVO列表)
     */
    List<TocEntryVO> generateOutline(AiReportCreateBO createBO);

    /**
     * 生成AI报告
     * 基于用户编辑后的大纲创建报告并生成完整内容
     *
     * @param generateBO 报告生成请求（包含基础信息和大纲）
     * @return 生成的报告内容
     */
    String generateAiReport(AiReportGenerateBO generateBO);

    /**
     * 流式生成AI报告
     * 基于用户编辑后的大纲创建报告并流式生成完整内容
     *
     * @param generateBO 报告生成请求（包含基础信息和大纲）
     * @return 流式响应
     */
    Flux<AiReportStreamResponse> generateAiReportStream(AiReportGenerateBO generateBO);

    /**
     * 获取AI报告详情
     *
     * @param reportId 报告ID
     * @return AI报告详情
     */
    AiReportDetailVO getAiReportDetail(Long reportId);

    SysAiReport getByReportId(Long reportId);

    /**
     * 保存报告编辑内容
     *
     * @param saveBO 报告保存请求
     * @return 保存结果
     */
    Boolean saveReport(SysReportSaveBO saveBO);
}
