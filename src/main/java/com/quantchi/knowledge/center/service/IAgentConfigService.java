package com.quantchi.knowledge.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.knowledge.center.bean.entity.SysAgentConfig;

import java.util.List;

/**
 * <p>
 * 智能体配置表 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface IAgentConfigService extends IService<SysAgentConfig> {
    
    /**
     * 获取智能体列表
     * 
     * @return 智能体列表JSON对象
     */
    List<SysAgentConfig> getAgentList();
    
    /**
     * 根据智能体类型获取API密钥
     * 
     * @param agentKey 智能体代号
     * @return API密钥
     */
    SysAgentConfig getApiKeyByAgentKey(String agentKey);
    

}
