package com.quantchi.knowledge.center.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.knowledge.center.bean.bo.CommonDataForCountBO;
import com.quantchi.knowledge.center.bean.bo.IndustryAnalysisFinancingScaleBO;
import com.quantchi.knowledge.center.bean.dto.KnowledgeNodeDTO;
import com.quantchi.knowledge.center.bean.entity.*;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.model.CustomIndexNavSetting;
import com.quantchi.knowledge.center.bean.model.Link;
import com.quantchi.knowledge.center.bean.model.NodeData;
import com.quantchi.knowledge.center.bean.model.Sankey;
import com.quantchi.knowledge.center.bean.vo.*;
import com.quantchi.knowledge.center.constants.Constant;
import com.quantchi.knowledge.center.controller.IndustryLinkAnalysisController;
import com.quantchi.knowledge.center.dao.mysql.*;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.IIndustryChainService;
import com.quantchi.knowledge.center.service.INodeService;
import com.quantchi.knowledge.center.service.cache.NodeCacheService;
import com.quantchi.knowledge.center.util.ChainNodeQueryUtil;
import com.quantchi.knowledge.center.util.DateHandlerUtil;
import com.quantchi.knowledge.center.util.NumberUtil;
import com.quantchi.knowledge.center.util.TreeBuildUtils;
import com.quantchi.knowledge.center.util.TreeListTransUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.quantchi.knowledge.center.config.MyBatisPlusConfig.DYNAMIC_TABLE_NAME_THREAD_LOCAL;
import static com.quantchi.knowledge.center.constants.Constant.NEWEST_YEAR;
import static com.quantchi.knowledge.center.controller.IndustryLinkAnalysisController.SCHEMA_ICD;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class NodeServiceImpl extends ServiceImpl<NodeMapper, Node> implements INodeService {

    private final IIndustryChainService knowledgeService;

    private final NodeMapper nodeMapper;

    private final CompanyNodeMapper companyNodeMapper;

    private final IndustryOverviewMapper industryOverviewMapper;

    private final ElasticsearchHelper elasticsearchHelper;

    private final DmDivisionMapper dmDivisionMapper;

    private final SchemaIcdMapper schemaIcdMapper;

    private final NodeCacheService nodeCacheService;

    /**
     * 构建桑基图数据
     *
     * @param nodeData
     * @param chainId
     * @return
     */
    private static Sankey transferNodeDataToSankey(final NodeData nodeData, final String chainId) {
        final List<KnowledgeNodeDTO> kgeNodeDTOList = new ArrayList<>();
        TreeListTransUtils.treeToList(kgeNodeDTOList, nodeData, chainId);
        final List<Link> linkList = new ArrayList<>();
        final Map<String, String> nodeNameMap = kgeNodeDTOList.stream().collect(Collectors.toMap(KnowledgeNodeDTO::getId, KnowledgeNodeDTO::getName, (v1, v2) -> v1));
        for (final KnowledgeNodeDTO kgeNodeDTO : kgeNodeDTOList) {
            final Link link = new Link();
            if ("root".equals(kgeNodeDTO.getParentId())) {
                continue;
            }
            link.setSource(nodeNameMap.get(kgeNodeDTO.getParentId()));
            link.setTarget(kgeNodeDTO.getName());
            linkList.add(link);
        }
        final Sankey sankey = new Sankey();
        sankey.setKgeNodeDTO(kgeNodeDTOList);
        sankey.setLinkList(linkList);
        return sankey;
    }

    /**
     * 初始化 cell 内容长度
     * cell 原本内容长度限制 32767  现修改为Integer.MAX_VALUE
     */
    public static void initCellMaxTextLength() {
        final SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
        if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
            final Field field;
            try {
                field = excel2007.getClass().getDeclaredField("_maxTextLength");
                field.setAccessible(true);
                field.set(excel2007, Integer.MAX_VALUE);
            } catch (final Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static List<List<String>> getTableHeading(final int maxLevel) {
        final List<List<String>> headingList = new ArrayList<>();
        for (int i = 0; i <= maxLevel; i++) {
            final List<String> title = new ArrayList<>();
            title.add(NumberUtil.int2chineseNum(i + 1) + "级领域");
            headingList.add(title);
        }
        headingList.add(new ArrayList<>(Collections.singletonList("英文名称")));
        headingList.add(new ArrayList<>(Collections.singletonList("节点简介")));
        headingList.add(new ArrayList<>(Collections.singletonList("ipc")));
        headingList.add(new ArrayList<>(Collections.singletonList("中文语义规则（多个语义规则顿号隔开）")));
        headingList.add(new ArrayList<>(Collections.singletonList("英文语义规则（多个语义规则顿号隔开）")));
        headingList.add(new ArrayList<>(Collections.singletonList("中文排除规则（多个语义规则顿号隔开）")));
        headingList.add(new ArrayList<>(Collections.singletonList("英文排除规则（多个语义规则顿号隔开）")));
        return headingList;
    }

    private static List<List<String>> getTableBody(final List<KnowledgeNodeDTO> kgeNodeDTOList) {
        final List<List<String>> bodyList = new ArrayList<>();
        //check the empty list
        if (CollectionUtils.isEmpty(kgeNodeDTOList)) {
            return bodyList;
        }
        //get the maxLevel
        final int maxLevel = kgeNodeDTOList.stream().map(KnowledgeNodeDTO::getLevel).max(Comparator.comparingInt(x -> x)).get();

        //change node to map (key nodeId value parentId)
        final Map<String, String> nodeIdParentIdMap = kgeNodeDTOList.stream().collect(Collectors.toMap(KnowledgeNodeDTO::getId, KnowledgeNodeDTO::getParentId, (v1, v2) -> v1));
        final Map<String, String> nodeIdNodeNameMap = kgeNodeDTOList.stream().collect(Collectors.toMap(KnowledgeNodeDTO::getId, KnowledgeNodeDTO::getName, (v1, v2) -> v1));

        for (final KnowledgeNodeDTO kgeNodeDTO : kgeNodeDTOList) {
            //build all null list
            final ArrayList<String> body = new ArrayList<>();
            for (int i = 0; i <= maxLevel; i++) {
                body.add(null);
            }
            //fill the body level info
            final Integer level = kgeNodeDTO.getLevel();
            String tempNodeId = kgeNodeDTO.getId();
            for (int i = level; i >= 0; i--) {
                final String nodeName = nodeIdNodeNameMap.get(tempNodeId);
                body.set(i, nodeName);
                tempNodeId = nodeIdParentIdMap.get(tempNodeId);
            }
            // 英文名称
            body.add(kgeNodeDTO.getNameEn());
            // 节点简介
            body.add(kgeNodeDTO.getDesc());
            // ipc
            body.add(kgeNodeDTO.getIpc());
            //Chinese semantic rules
            if (kgeNodeDTO.getKeywordZh() != null) {
                body.add(String.join("、", kgeNodeDTO.getKeywordZh()));
            }
            //English semantic rules
            if (kgeNodeDTO.getKeywordEn() != null) {
                body.add(String.join("、", kgeNodeDTO.getKeywordEn()));
            }
            //Chinese exclude rules
            if (kgeNodeDTO.getExcludeKeywordZh() != null) {
                body.add(String.join("、", kgeNodeDTO.getExcludeKeywordZh()));
            }
            //English exclude rules
            if (kgeNodeDTO.getExcludeKeywordEn() != null) {
                body.add(String.join("、", kgeNodeDTO.getExcludeKeywordEn()));
            }
            //add to the bodyList
            bodyList.add(body);
        }
        return bodyList;
    }

    /**
     * 获取链图节点树数据
     */
    @Override
    public KnowledgeSystemVO selectKnowledgeInfo(final String chainId, final String structureType) {
        final KnowledgeSystemVO kgeSystemVO = new KnowledgeSystemVO();
        // 从链图元数据表里获取节点数据
        final Knowledge knowledge = knowledgeService.getById(chainId);
        BeanUtils.copyProperties(knowledge, kgeSystemVO);
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        // 查询国内企业+国外企业数量
        kgeSystemVO.setCompanyCount(nodeCacheService.getNodeAllCompanyCount(chainId));
        // 查询对应技术节点数量
        kgeSystemVO.setTechCount(nodeCacheService.countTechByChainId(chainId));
        // 从链图节点表里获取节点数据
        final List<Node> nodeList = this.list(Wrappers.<Node>lambdaQuery()
                .eq(Node::getIsValid, 1));
        kgeSystemVO.setNodeCount(nodeList.size());
        final NodeData nodeData = TreeListTransUtils.listToTree(nodeList);
        // 构建桑基图数据
        if ("sankey".equals(structureType)) {
            //set sankey message
            kgeSystemVO.setSankey(transferNodeDataToSankey(nodeData, chainId));
        } else {
            //set nodeData message
            kgeSystemVO.setNodeData(nodeData);
        }
        return kgeSystemVO;
    }

    @Override
    public NodeData selectNodeData(final String chainId, final Integer level) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        // 从链图节点表里获取节点数据
        final List<Node> nodeList = this.list(Wrappers.<Node>lambdaQuery()
                .eq(Node::getIsValid, 1)
                .le(Node::getLevel, level));
        return TreeListTransUtils.listToTree(nodeList);
    }

    /**
     * 导出链图设计Excel
     */
    @Override
    public void exportExcel(final HttpServletResponse response, final String chainId) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final List<Node> nodeList = this.list(Wrappers.<Node>lambdaQuery()
                .eq(Node::getIsValid, 1));
        final NodeData nodeData = TreeListTransUtils.listToTree(nodeList);
        final List<KnowledgeNodeDTO> kgeNodeSortList = new ArrayList<>();
        TreeListTransUtils.treeToList(kgeNodeSortList, nodeData, chainId);

        //generate excel file
        final Integer maxLevel = kgeNodeSortList.stream().map(KnowledgeNodeDTO::getLevel)
                .max(Comparator.comparingInt(x -> x)).get();
        final List<List<String>> tableHeading = getTableHeading(maxLevel);
        final List<List<String>> tableBody = getTableBody(kgeNodeSortList);
        initCellMaxTextLength();
        try {
            // 从链图元数据表里获取节点数据
            final Knowledge knowledge = knowledgeService.getById(chainId);
            final String fileName = "产业图谱_" + knowledge.getName() + "_" + knowledge.getKnowledgeType();
            response.reset();
            response.setHeader("content-Type", "application/vnd.ms-files");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
            response.setContentType("application/xls;charset=UTF-8");
            //user easyExcel tool to output
            EasyExcelFactory.write(response.getOutputStream())
                    .head(tableHeading)
                    .inMemory(Boolean.TRUE)
                    .sheet("sheet1")
                    .doWrite(tableBody);
        } catch (final Exception e) {
            log.error("导出有误:{}", chainId, e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 根据节点id获取当前节点及其子节点id列表
     *
     * @param chainId
     * @param nodeId
     * @return
     */
    @Override
    public List<String> getChildNodeIdList(final String chainId, final String nodeId) {
        if (CharSequenceUtil.isBlank(chainId)) {
            return Collections.singletonList(nodeId);
        }
        return getChildNodeIdList(chainId, Collections.singletonList(nodeId));
    }

    /**
     * 获取多个节点ID及其所有子节点的ID列表
     *
     * @param chainId 产业链ID
     * @param nodeIdList 节点ID列表
     * @return 包含所有节点及其子节点的ID列表
     */
    @Override
    public List<String> getChildNodeIdList(final String chainId, final List<String> nodeIdList) {
        if (CollUtil.isEmpty(nodeIdList)) {
            return new ArrayList<>();
        }

        final List<Node> nodeList;
        if (Objects.equals(chainId, IndustryLinkAnalysisController.SCHEMA_ICD)) {
            // 处理 SCHEMA_ICD 的情况
            return getChildNodeIdListForSchemaIcd(nodeIdList);
        } else {
            // 处理其他产业链的情况
            DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
            nodeList = this.list(Wrappers.<Node>lambdaQuery()
                    .eq(Node::getIsValid, 1));
        }

        // 如果节点ID列表为空，返回所有节点
        if (nodeIdList.isEmpty()) {
            return nodeList.stream().map(Node::getId).collect(Collectors.toList());
        }

        final NodeData nodeData = TreeListTransUtils.listToTree(nodeList);
        final Set<String> allChildNodeSet = new HashSet<>();

        if (nodeData != null) {
            for (final String nodeId : nodeIdList) {
                if (CharSequenceUtil.isNotBlank(nodeId)) {
                    final List<String> childNodeList = nodeData.getAllChildrenIds(nodeId);
                    allChildNodeSet.addAll(childNodeList);
                }
            }
        } else {
            // 如果无法构建树结构，直接返回输入的节点ID列表
            allChildNodeSet.addAll(nodeIdList);
        }

        return new ArrayList<>(allChildNodeSet);
    }

    /**
     * 获取 SCHEMA_ICD 类型的多个节点ID及其所有子节点的ID列表
     *
     * @param nodeIdList 节点ID列表
     * @return 包含所有节点及其子节点的ID列表
     */
    private List<String> getChildNodeIdListForSchemaIcd(final List<String> nodeIdList) {
        final Set<String> allNodeIdSet = new HashSet<>(nodeIdList);

        for (final String nodeId : nodeIdList) {
            if (CharSequenceUtil.isBlank(nodeId)) {
                continue;
            }

            final SchemaIcd schemaIcd = schemaIcdMapper.selectById(nodeId);
            if (schemaIcd == null) {
                continue;
            }

            // 获取当前节点的所有子节点
            List<SchemaIcd> schemaIcds = schemaIcdMapper.selectList(Wrappers.<SchemaIcd>lambdaQuery()
                    .eq(SchemaIcd::getParentId, schemaIcd.getId()));
            while (CollUtil.isNotEmpty(schemaIcds)) {
                final List<String> childNodeIdList = schemaIcds.stream().map(SchemaIcd::getId).collect(Collectors.toList());
                allNodeIdSet.addAll(childNodeIdList);
                schemaIcds = schemaIcdMapper.selectList(Wrappers.<SchemaIcd>lambdaQuery()
                        .in(SchemaIcd::getParentId, childNodeIdList));
            }
        }

        return new ArrayList<>(allNodeIdSet);
    }

    /**
     * 企业成立年限数量分布
     */
    @Override
    public NameCountWithSumVO getEstablishmentYears(final String chainId, final String nodeId) {
        final NameCountWithSumVO result = new NameCountWithSumVO();
        final List<NameCountVO> resultList = new ArrayList<>();
        final List<String> childNodeList;
        if (CharSequenceUtil.isBlank(nodeId)) {
            childNodeList = null;
        } else {
            childNodeList = getChildNodeIdList(chainId, nodeId);
        }
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final EstablishYearVO establishmentYears = nodeMapper.getEstablishmentYears(childNodeList, null, null, null);
        if (establishmentYears != null) {
            resultList.add(new NameCountVO("3月内", establishmentYears.getWithin3months()));
            resultList.add(new NameCountVO("3月-6月", establishmentYears.getWithin3To6Months()));
            resultList.add(new NameCountVO("6月-1年", establishmentYears.getWithin6MonthsTo1Year()));
            resultList.add(new NameCountVO("1-3年", establishmentYears.getWithin1YearTo3Years()));
            resultList.add(new NameCountVO("3-5年", establishmentYears.getWithin3YearsTo5Years()));
            resultList.add(new NameCountVO("5-10年", establishmentYears.getWithin5YearsTo10Years()));
            resultList.add(new NameCountVO("10年以上", establishmentYears.getOver10Years()));
            result.setRecords(resultList);
            result.setSum(resultList.stream().mapToLong(NameCountVO::getCount).sum());
//            // 计算比率
//            resultList.forEach(item -> item.setCount(DataUtil.calculatePercentage(item.getCount(), result.getSum())));
        } else {
            result.setRecords(Collections.emptyList());
            result.setSum(0L);
        }
        return result;
    }

    /**
     * 企业注册资本分布
     *
     * @param chainId
     * @param nodeId
     * @return
     */
    @Override
    public NameCountWithSumVO aggRegistCapiValue(final String chainId, final String nodeId) {
        final NameCountWithSumVO result = new NameCountWithSumVO();
        final List<NameCountVO> resultList = new ArrayList<>();
        final List<String> childNodeList;
        if (CharSequenceUtil.isBlank(nodeId)) {
            childNodeList = null;
        } else {
            childNodeList = getChildNodeIdList(chainId, nodeId);
        }
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final CountVO countVO = nodeMapper.aggRegistCapiValue(childNodeList, null, null, null);
        if (countVO != null) {
            resultList.add(new NameCountVO("100万以内", countVO.getOne()));
            resultList.add(new NameCountVO("100-200万", countVO.getTwo()));
            resultList.add(new NameCountVO("200-500万", countVO.getThree()));
            resultList.add(new NameCountVO("500-1000万", countVO.getFour()));
            resultList.add(new NameCountVO("1000-5000万", countVO.getFive()));
            resultList.add(new NameCountVO("5000万以上", countVO.getSix()));
            result.setRecords(resultList);
            result.setSum(resultList.stream().mapToLong(NameCountVO::getCount).sum() + countVO.getZero());
//            // 计算比率
//            resultList.forEach(item -> item.setCount(DataUtil.calculatePercentage(item.getCount(), result.getSum())));
        } else {
            result.setRecords(Collections.emptyList());
            result.setSum(0L);
        }
        return result;
    }

    @Override
    public List<NameCountVO> getHighQualityEnterprises(final String chainId, final String nodeId) {
        final List<NameCountVO> resultList = new ArrayList<>();
        final List<String> childNodeList = getChildNodeIdList(chainId, nodeId);
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final List<NameCountVO> allTagList = nodeMapper.aggTagWithoutRegion(childNodeList);
        if (CollUtil.isNotEmpty(allTagList)) {
            final Map<String, Long> allTagMap = allTagList.stream().collect(Collectors.toMap(NameCountVO::getName, NameCountVO::getCount));
            resultList.add(new NameCountVO("上市企业", allTagMap.getOrDefault("上市企业", 0L)));
            resultList.add(new NameCountVO("专精特新企业", allTagMap.getOrDefault("专精特新企业", 0L)));
            resultList.add(new NameCountVO("高新技术企业", allTagMap.getOrDefault("高新技术企业", 0L)));
            resultList.add(new NameCountVO("所有重点企业", industryOverviewMapper.getKeyNoteCompanyCount(Constant.TAGS, childNodeList)));
        }
        return resultList;
    }

    @Override
    public List<NameCountVO> getRegionDistribution(final String chainId, final String nodeId) {
        final List<String> childNodeList = getChildNodeIdList(chainId, nodeId);
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        //使用city_code聚合而不直接使用city聚合是因为聚合数量会有差异，根本原因是city和code不一致
        //为了和企业列表地区筛选保持一致，所以先用code聚合，再做替换
        final List<NameCountVO> list = nodeMapper.aggCity(childNodeList);
        if (!CollectionUtils.isEmpty(list)) {
            for (final NameCountVO vo : list) {
                final DmDivision division = dmDivisionMapper.selectById("division/" + vo.getName());
                if (division != null) {
                    vo.setName(division.getName());
                }
            }
        }

        return list;
    }

    /**
     * 产业融资规模
     *
     * @param chainId
     * @param nodeId
     * @return
     */
    @Override
    public List<MultiBarChartVO> getFinancingScale(final String chainId, final String nodeId) {
        final List<MultiBarChartVO> result = new ArrayList<>();
        final List<String> childNodeList = getChildNodeIdList(chainId, nodeId);
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final int beginYear = NEWEST_YEAR - 6;
        final List<IndustryAnalysisFinancingScaleBO> list = companyNodeMapper.getFinancingScale(childNodeList, beginYear + "-12-31");
        if (!CollectionUtils.isEmpty(list)) {
            final List<NameValueVO> sumList = new ArrayList<>();
            final List<NameValueVO> countList = new ArrayList<>();
            final List<NameValueVO> companyCountList = new ArrayList<>();
            for (final IndustryAnalysisFinancingScaleBO bo : list) {
                sumList.add(new NameValueVO(bo.getYear(), bo.getSum()));
                countList.add(new NameValueVO(bo.getYear(), bo.getCount()));
                companyCountList.add(new NameValueVO(bo.getYear(), bo.getCompanyCount()));
            }
            final MultiBarChartVO sum = new MultiBarChartVO();
            sum.setName("融资金额");
            sum.setList(sumList);
            result.add(sum);
            final MultiBarChartVO count = new MultiBarChartVO();
            count.setName("融资笔数");
            count.setList(countList);
            result.add(count);
            final MultiBarChartVO companyCount = new MultiBarChartVO();
            companyCount.setName("融资企业数");
            companyCount.setList(companyCountList);
            result.add(companyCount);
        }
        return result;
    }

    @Override
    public List<CommonDataForCountBO> companyScale(final String chainId, final String nodeId,
                                                   final String province, final String city, final String area) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final List<CommonDataForCountBO> list = companyNodeMapper.getCompanyScale(getChildNodeIdList(chainId, nodeId),
                province, city, area);
        int total = 0;
        for (final CommonDataForCountBO bo : list) {
            total = total + bo.getCount();
            String newName = "";
            if (StringUtils.isEmpty(bo.getName())) {
                newName = "其他";
            } else {
                switch (bo.getName()) {
                    case "大型":
                        newName = "大型企业";
                        break;
                    case "中型":
                        newName = "中型企业";
                        break;
                    case "小型":
                        newName = "小型企业";
                        break;
                    case "微型":
                        newName = "微型企业";
                        break;
                }
            }
            bo.setName(newName);
        }
        final Map<String, CommonDataForCountBO> map = new HashMap<>();
        for (final CommonDataForCountBO bo : list) {
            if (total != 0) {
                bo.setData(new BigDecimal(bo.getCount())
                        .divide(new BigDecimal(total), 3, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100))
                        .setScale(1, RoundingMode.HALF_UP)
                        .toString());
            }
            map.put(bo.getName(), bo);
        }
        //排序
        final List<String> orderList = Arrays.asList("大型企业", "中型企业", "小型企业", "微型企业", "其他");
        final List<CommonDataForCountBO> result = new ArrayList<>();
        for (final String name : orderList) {
            if (map.get(name) != null) {
                result.add(map.get(name));
            }
        }
        return result;
    }

    /**
     * 获取节点对应的专利类型年份分布数据
     *
     * @param chainId
     * @param nodeId
     * @return
     */
    @Override
    public List<MultiBarChartVO> getPatentType(final String chainId, final String nodeId) {
        final List<String> nodeIdList = getChildNodeIdList(chainId, nodeId);
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        final int beginYear = NEWEST_YEAR - 6;
        boolQueryBuilder.filter(ChainNodeQueryUtil.buildChainNodeQuery(chainId, nodeIdList));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("apply_date").gt(beginYear + "-12-31"));
        boolQueryBuilder.filter(QueryBuilders.termsQuery("patent_type", Arrays.asList("外观设计", "实用新型", "发明申请")));
        final Map<String, List<NameCountVO>> patentTypeMap = elasticsearchHelper.getYearTermAggregations(EsIndexEnum.PATENT.getEsIndex(), "apply_date", "patent_type", boolQueryBuilder, 1000, 10000);
        final List<MultiBarChartVO> result = new ArrayList<>();
        final List<NameValueVO> waiguanList = new ArrayList<>();
        final List<NameValueVO> shiyongList = new ArrayList<>();
        final List<NameValueVO> famingList = new ArrayList<>();
        final List<String> years = DateHandlerUtil.getYears(String.valueOf(beginYear + 1), String.valueOf(NEWEST_YEAR));
        years.forEach(year -> {
            waiguanList.add(new NameValueVO(year, patentTypeMap.getOrDefault(year, Collections.emptyList()).stream().filter(item -> "外观设计".equals(item.getName())).mapToLong(NameCountVO::getCount).sum()));
            shiyongList.add(new NameValueVO(year, patentTypeMap.getOrDefault(year, Collections.emptyList()).stream().filter(item -> "实用新型".equals(item.getName())).mapToLong(NameCountVO::getCount).sum()));
            famingList.add(new NameValueVO(year, patentTypeMap.getOrDefault(year, Collections.emptyList()).stream().filter(item -> "发明申请".equals(item.getName())).mapToLong(NameCountVO::getCount).sum()));
        });
        result.add(new MultiBarChartVO("外观设计", waiguanList));
        result.add(new MultiBarChartVO("实用新型", shiyongList));
        result.add(new MultiBarChartVO("发明专利", famingList));
        return result;
    }

    @Override
    public List<CommonDataForCountBO> getNationalIndustry(final String chainId, final String nodeId) {
        final List<String> nodeIdList = getChildNodeIdList(SCHEMA_ICD, nodeId);
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final List<CommonDataForCountBO> list = companyNodeMapper.getNationalIndustry(nodeIdList);
        final List<CommonDataForCountBO> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            //前五名及其他
            int total = 0;
            int num = 0;
            int other = 0;
            for (final CommonDataForCountBO bo : list) {
                total = total + bo.getCount();
                if (num <= 4) {
                    result.add(bo);
                } else {
                    other = other + bo.getCount();
                }
                num++;

            }
            if (other > 0) {
                final CommonDataForCountBO otherBO = new CommonDataForCountBO();
                otherBO.setName("其他");
                otherBO.setCount(other);
                result.add(otherBO);
            }
            //计算占比
            if (total != 0) {
                for (final CommonDataForCountBO bo : result) {
                    bo.setData(new BigDecimal(bo.getCount())
                            .divide(new BigDecimal(total), 3, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(100))
                            .setScale(1, RoundingMode.HALF_UP)
                            .toString());
                }
            }

        }
        return result;
    }

    @Override
    public List<CustomIndexNavSetting> getTree(String chainId) {

        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);

        final List<Node> nodeList = this.list(Wrappers.<Node>lambdaQuery()
                        .select(Node::getId, Node::getParentId, Node::getName, Node::getLevel)
                .eq(Node::getIsValid, 1)
                .orderByAsc(Node::getLevel));

        final List<Tree<String>> treeList = TreeBuildUtils.keywordSearch(TreeBuildUtils.build(nodeList, (item, tree) -> {
            tree.setId(item.getId())
                    .setParentId(item.getParentId())
                    .setName(item.getName())
                    .setWeight(item.getLevel());
            tree.putExtra("type", chainId.replace("industry_",""));
        }), null);

        final List<CustomIndexNavSetting> result = CustomIndexNavSetting.convertTreeList(treeList.get(0).getChildren());

        // 处理field，在前面拼接chainId
        processFieldWithChainId(result, chainId);

        return result;

    }

    /**
     * 递归处理CustomIndexNavSetting列表，在field前面拼接chainId
     */
    private void processFieldWithChainId(List<CustomIndexNavSetting> settingList, String chainId) {
        if (settingList == null || settingList.isEmpty()) {
            return;
        }

        for (CustomIndexNavSetting setting : settingList) {
            // 在field前面拼接chainId
            if (setting.getField() != null) {
                setting.setField(chainId + ":" + setting.getField());
            }

            // 递归处理子节点
            if (setting.getChildren() != null && !setting.getChildren().isEmpty()) {
                processFieldWithChainId(setting.getChildren(), chainId);
            }
        }
    }

}
