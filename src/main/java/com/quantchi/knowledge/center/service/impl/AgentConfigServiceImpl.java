package com.quantchi.knowledge.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.quantchi.knowledge.center.bean.entity.SysAgentConfig;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.dao.mysql.AgentConfigMapper;
import com.quantchi.knowledge.center.service.IAgentConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 智能体配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Service
@Slf4j
public class AgentConfigServiceImpl extends ServiceImpl<AgentConfigMapper, SysAgentConfig> implements IAgentConfigService {

    @Override
    public List<SysAgentConfig> getAgentList() {
        PageHelper.startPage(1, 10);
        // 查询所有启用的智能体配置
        final List<SysAgentConfig> sysAgentConfigs = this.list(
                new LambdaQueryWrapper<SysAgentConfig>()
                        .eq(SysAgentConfig::getEnabled, true)
                        .orderByAsc(SysAgentConfig::getSort)
        );
        return sysAgentConfigs;
    }
    
    @Override
    public SysAgentConfig getApiKeyByAgentKey(final String agentKey) {
        if (agentKey == null) {
            return null;
        }
        
        // 查询指定代号的智能体配置
        final SysAgentConfig sysAgentConfig = this.getOne(
                new LambdaQueryWrapper<SysAgentConfig>()
                        .eq(SysAgentConfig::getAgentKey, agentKey)
                        .eq(SysAgentConfig::getEnabled, true)
        );
        if (sysAgentConfig == null) {
            throw new BusinessException("找不到对应的智能体配置");
        }

        return sysAgentConfig;
    }

}
