package com.quantchi.knowledge.center.service.impl;

import com.quantchi.knowledge.center.bean.entity.SysAiReport;
import com.quantchi.knowledge.center.bean.entity.SysReport;
import com.quantchi.knowledge.center.bean.enums.ReportTypeEnum;
import com.quantchi.knowledge.center.bean.system.bo.SysReportDownloadBO;
import com.quantchi.knowledge.center.service.ISysAiReportService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * SysReportServiceImpl 测试类
 * 主要测试AI报告下载功能
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@ExtendWith(MockitoExtension.class)
class SysReportServiceImplTest {

    @Mock
    private ISysAiReportService sysAiReportService;

    @Mock
    private ReportPreserveService reportPreserveService;

    @InjectMocks
    private SysReportServiceImpl sysReportService;

    @Test
    void testDownloadReport_AiReport() {
        // 准备测试数据
        final Long reportId = 1L;
        final SysReportDownloadBO downloadBO = new SysReportDownloadBO();
        downloadBO.setReportId(reportId);

        // 创建AI报告对象
        final SysReport sysReport = new SysReport();
        sysReport.setId(reportId);
        sysReport.setTitle("测试AI报告");
        sysReport.setType(ReportTypeEnum.AI_REPORT.getId());
        sysReport.setDownloadTimes(0);

        final SysAiReport aiReport = new SysAiReport();
        aiReport.setReportId(reportId);
        aiReport.setContent("<html><body><h1>测试AI报告内容</h1><p>这是一个测试报告。</p></body></html>");

        // 模拟方法调用
        when(sysAiReportService.getByReportId(reportId)).thenReturn(aiReport);

        // 验证报告类型判断逻辑
        assertEquals(ReportTypeEnum.AI_REPORT.getId(), sysReport.getType());
        assertNotNull(aiReport.getContent());
        assertTrue(aiReport.getContent().contains("测试AI报告内容"));

        // 验证AI报告服务调用
        verify(sysAiReportService, times(1)).getByReportId(reportId);
    }

    @Test
    void testReportPreserveServiceIntegration() throws Exception {
        // 测试ReportPreserveService集成
        final String htmlContent = "<html><body><h1>测试内容</h1></body></html>";
        final String title = "测试报告";
        final File mockPdfFile = new File("test.pdf");

        // 模拟ReportPreserveService的方法调用
        when(reportPreserveService.generatePdfFromHtml(htmlContent, title)).thenReturn(mockPdfFile);

        // 调用方法
        final File result = reportPreserveService.generatePdfFromHtml(htmlContent, title);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockPdfFile, result);
        verify(reportPreserveService, times(1)).generatePdfFromHtml(htmlContent, title);
    }

    @Test
    void testDownloadReport_NormalReport() {
        // 准备测试数据
        final Long reportId = 2L;
        final SysReportDownloadBO downloadBO = new SysReportDownloadBO();
        downloadBO.setReportId(reportId);

        // 创建普通报告对象
        final SysReport sysReport = new SysReport();
        sysReport.setId(reportId);
        sysReport.setTitle("测试普通报告");
        sysReport.setType(ReportTypeEnum.NORMAL_REPORT.getId());
        sysReport.setFileId(100L);
        sysReport.setDownloadTimes(0);

        // 验证报告类型判断逻辑
        assertEquals(ReportTypeEnum.NORMAL_REPORT.getId(), sysReport.getType());
        assertNotNull(sysReport.getFileId());

        // 对于普通报告，不应该调用AI报告服务
        verify(sysAiReportService, never()).getByReportId(anyLong());
    }

    @Test
    void testReportTypeEnumValues() {
        // 验证报告类型枚举值
        assertEquals((byte) 1, ReportTypeEnum.NORMAL_REPORT.getId());
        assertEquals((byte) 2, ReportTypeEnum.AI_REPORT.getId());
        assertEquals("普通报告", ReportTypeEnum.NORMAL_REPORT.getDesc());
        assertEquals("AI报告", ReportTypeEnum.AI_REPORT.getDesc());
    }
}
